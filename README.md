# 🛠️ Config Management
This service provides centralized configuration management for microservices using Spring Cloud Config Server. It loads configuration properties from a Git repository and exposes them to client services at runtime.

---

## 📦 Requirements
- Java 17+
- Git repository with configuration files

---

## ⚙️ Configuration
y default, the Config Server expects the following properties in `application.properties`:
```properties
spring.application.name=config-server
server.port=8888

# Git repo for configuration
spring.cloud.config.server.git.uri=${CONFIG_REPO_URI}
spring.cloud.config.server.git.username=${GIT_USERNAME}
spring.cloud.config.server.git.password=${GIT_TOKEN}
```

---

## 🔐 Environment Variables
Set the following environment variables for secure access to the Git repository:
```env
CONFIG_REPO_URI=https://gitlab.com/your-org/your-config-repo
CONFIG_REPO_USERNAME=git_username
CONFIG_REPO_PASSWORD=git_token_with_access_to_read_repository
```