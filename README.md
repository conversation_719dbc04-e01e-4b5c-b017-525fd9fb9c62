# 💱 Currency Gateway

**Currency Gateway** is a Spring Boot microservice that:

- Connects to Binance WebSocket API to stream real-time cryptocurrency prices
- Dynamically refreshes configuration via Spring Cloud Config Server
- Publishes price events to Kafka for further processing or analytics

---

## 🔍 Features

- 📡 **Real-Time Price Feed**: Connects to Binance WebSocket streams
- 🔁 **Dynamic Config Refresh**: Updates trading pairs and intervals without restarting the app
- 📦 **Kafka Integration**: Sends price events to Kafka as JSON messages
- 📈 **Scheduled Aggregation**: Groups and sends data in batches
- ☁️ **Spring Cloud Compatible**: Integrates with Spring Cloud Config and Actuator

---

## 🔧 Configuration

Example configuration from `application.yml` or Spring Cloud Config Server:

```yaml
spring:
  kafka:
    bootstrap-servers: ${KAFKA_NAME:localhost}:${KAFKA_PORT:9092}
    consumer:
      group-id: kafka-router
      auto-offset-reset: earliest

binance:
  pairs:
    - BTCUSDC
    - ETHUSDC
    - SOL<PERSON><PERSON>
  kafkaTopicSendPriceUpdate: crypto.prices.update

kafka:
  topic:
    input: ${KAFKA_TOPIC_INPUT:crypto.prices.update}
    output: ${KAFKA_TOPIC_OUTPUT:crypto.prices.broadcast}
```

---

## 🔄 Configuration Refresh

- ⏰ **Automatic**: The service checks for config updates daily at 00:00 UTC
- 🧩 **Manual**:

```http
POST /actuator/refresh
```

This reloads the configuration from the Config Server and applies it immediately.

---

## 📤 Kafka Message Format

Each update is published as a JSON object:

```json
{
  "symbol": "BTCUSDC",
  "price": 68923.15,
  "volume": 112.5,
  "timestamp": "2025-05-09T08:15:30Z"
}
```

---

## 📂 Environment Variables

Before starting the application, define the following variables:

```env
KAFKA_NAME=localhost
KAFKA_PORT=9092
CONFIG_NAME=config-server-host
CONFIG_PORT=8888
BRANCH_NAME=main
KAFKA_TOPIC_INPUT=crypto.prices.update
KAFKA_TOPIC_OUTPUT=crypto.prices.broadcast
```

---

## 🐳 Dockerized Setup

Kafka and Zookeeper can be started using Docker Compose. See the provided `docker-compose.yml`.

---

## 🚀 Requirements

- Java 17+
- Kafka cluster
- Spring Cloud Config Server (port 8888)
- Access to wss://stream.binance.com

---

## 🧪 Tests

Project includes unit tests using:
- JUnit 5
- Mockito
- Spring Boot Test

---

## 📁 Project Structure

- `ConfigRefresher` — Handles configuration reloads
- `KafkaForwarderService` — Sends messages to Kafka
- `BinanceClientService` — Connects to Binance WebSocket
- `CryptoPriceEvent` — DTO for price messages

---

## ✅ Status

CI/CD Pipeline: ✅  
Kafka Integration: ✅  
WebSocket API: ✅  
Config Refresh: ✅
