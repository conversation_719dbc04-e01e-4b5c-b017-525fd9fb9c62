pipeline {
    agent any
    tools {
        maven 'default maven'
    }

    options {
        gitLabConnection('default')
    }

    stages {
        stage('Code Formatting') {
            steps {
                sh 'mvn spotless:apply'
            }
        }

        stage('Code Quality Checks') {
            steps {
                sh 'mvn checkstyle:check'
            }
        }

        stage('Static Analysis (SpotBugs)') {
           steps {
               sh 'mvn spotbugs:check'
           }
        }

        stage('Test & Coverage') {
            steps {
                sh 'mvn clean verify'
            }
        }
    }
     post {
        success {
            updateGitlabCommitStatus name: 'jenkins', state: 'success'
        }
        failure {
            updateGitlabCommitStatus name: 'jenkins', state: 'failed'
        }
    }

}
