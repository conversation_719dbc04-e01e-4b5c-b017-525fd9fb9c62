pipeline {
  agent any
    tools {
    nodejs 'NodeJS 24'
    }

    options {
    gitLabConnection('default')
    }

    stages {
    stage('Install Dependencies') {
      steps {
        sh 'npm install'
      }
    }
        stage('Lint') {
      steps {
        sh 'npm run lint'
            }
        }
    }
     post {
    success {
      updateGitlabCommitStatus name: 'jen<PERSON>', state: 'success'
        }
        failure {
      updateGitlabCommitStatus name: 'jen<PERSON>', state: 'failed'
        }
    }

}
