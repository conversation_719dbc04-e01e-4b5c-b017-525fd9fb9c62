pipeline {
    agent any
    tools {
        maven 'default maven'
    }

    options {
        gitLabConnection('default')
    }

    stages {
        stage('Code Formatting') {
            steps {
                sh 'mvn spotless:apply'
            }
        }

        stage('Code Quality Checks') {
            steps {
                sh 'mvn checkstyle:check'
            }
        }
    }
     post {
        success {
            updateGitlabCommitStatus name: 'jen<PERSON>', state: 'success'
        }
        failure {
            updateGitlabCommitStatus name: 'jen<PERSON>', state: 'failed'
        }
    }

}
