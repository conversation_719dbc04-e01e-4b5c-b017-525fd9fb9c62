version: '3.8'
services:
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.4
    container_name: zookeeper
    environment:
      ZOOKEEPER_SERVER_ID: 1
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    healthcheck:
      test: ["CMD-SHELL", "echo srvr | nc localhost 2181 | grep 'Mode: ' || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 60s
    networks:
      - network
  
  kafka:
    image: confluentinc/cp-kafka:7.4.4
    container_name: kafka
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
      KAFKA_NUM_PARTITIONS: 3
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:9092
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 100
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_LOG_RETENTION_HOURS: 24
    healthcheck:
      test: [ "CMD-SHELL", "kafka-topics --bootstrap-server kafka:9092 --list" ]
      interval: 15s
      timeout: 10s
      retries: 5
    depends_on:
      zookeeper:
        condition: service_healthy
    networks:
      - network
  
  config-management:
    container_name: config-management
    build:
      context: ./config-server
      dockerfile: Dockerfile
    ports:
      - "8888:8888"
    networks:
      - network
  
  currency-service:
    container_name: currency-service
    build:
      context: ./currency-service
      dockerfile: Dockerfile
    ports:
      - "8082:8080"
    depends_on:
      currency-db:
        condition: service_healthy
      kafka:
        condition: service_healthy
    networks:
      - network
  
  currency-db:
    image: 'mongo:8.0.6'
    container_name: currency-db
    environment:
      - 'MONGO_INITDB_DATABASE=currencyDB'
      - 'MONGO_INITDB_ROOT_PASSWORD=root'
      - 'MONGO_INITDB_ROOT_USERNAME=root'
    ports:
      - '27017:27017'
    volumes:
      - currency_db_data:/data/db
    networks:
      - network
    healthcheck:
      test: [ "CMD", "mongosh", "--eval", "db.runCommand({ping:1})" ]
      interval: 10s
      timeout: 5s
      retries: 5
  eureka:
    image: steeltoeoss/eureka-server
    container_name: eureka-server    
    ports:
      - "${EUREKA_PORT}:8761"
    networks:
      - network

  api-gateway:
    build:
      context: ./api-gateway
    container_name: api-gateway
    ports:
      - "${API_GATEWAY_PORT}:8080"
    environment:
      - EUREKA_SERVER_URL=${EUREKA_SERVER_URL}
    depends_on:
      - eureka
    networks:
      - network
  currency-gateway:
    container_name: currency-gateway
    build:
      context: ./currency-gateway
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    depends_on:
      config-management:
        condition: service_started
      kafka:
        condition: service_healthy
    networks:
      - network
  postgres:
    image: postgres:15
    container_name: user-service-db
    environment:
      POSTGRES_USER: ${USER_DB_USER}
      POSTGRES_PASSWORD: ${USER_DB_PASSWORD}
      POSTGRES_DB: postgres
      PGDATA: /data/postgres
    ports:
      - "5434:5432"
    volumes:
      - postgres-data:/data/postgres
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5



volumes:
  currency_db_data:
  postgres-data:

networks:
  network:
    driver: bridge
    name: network