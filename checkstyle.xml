<?xml version="1.0"?>
<!DOCTYPE module PUBLIC
          "-//Checkstyle//DTD Checkstyle Configuration 1.3//EN"
          "https://checkstyle.org/dtds/configuration_1_3.dtd">

<module name="Checker">
    
    <!-- Define the tree walker -->
    <module name="TreeWalker">

        <!-- Checks for naming conventions -->
        <module name="TypeName"/>
        <module name="MethodName"/>
        <module name="ParameterName"/>
        <module name="LocalVariableName"/>
        <module name="MemberName"/>

        <!-- Indentation -->
        <module name="Indentation">
            <property name="basicOffset" value="4"/>
            <property name="braceAdjustment" value="0"/>
            <property name="caseIndent" value="4"/>
            <property name="tabWidth" value="4"/>
        </module>

        <!-- No empty code blocks -->
        <module name="EmptyBlock">
            <property name="option" value="TEXT"/>
        </module>

        <!-- Check for unused imports -->
        <module name="UnusedImports"/>
    </module>

</module>

