import { Injectable, signal } from '@angular/core';
import { Observable, of, throwError } from 'rxjs';
import { delay, tap } from 'rxjs/operators';
import {User} from './user.model';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly storageKey = 'user_auth';
  private readonly userSignal = signal<User | null>(null);

  constructor() {
    const storedAuth = localStorage.getItem(this.storageKey);
    if (storedAuth) {
      try {
        const userData = JSON.parse(storedAuth);
        this.userSignal.set({
          email: userData.email,
          timestamp: new Date(userData.timestamp)
        });
      } catch (error) {
        localStorage.removeItem(this.storageKey);
      }
    }
  }

  get user() {
    return this.userSignal();
  }

  isAuthenticated(): boolean {
    return this.userSignal() !== null;
  }

  login(email: string, password: string): Observable<any> {
    if (!email || !password) {
      return throwError(() => new Error('Email and password are required'));
    }

    if (password.length < 6) {
      return throwError(() => new Error('Password must be at least 6 characters'));
    }

    const userData: User = { email, timestamp: new Date() };

    return of({ success: true, email }).pipe(
      delay(1000),
      tap(() => {
        this.userSignal.set(userData);
        localStorage.setItem(this.storageKey, JSON.stringify(userData));
      })
    );
  }

  signup(email: string, password: string): Observable<any> {
    if (!email || !password) {
      return throwError(() => new Error('Email and password are required'));
    }

    if (password.length < 6) {
      return throwError(() => new Error('Password must be at least 6 characters'));
    }

    const userData: User = { email, timestamp: new Date() };

    return of({ success: true, email }).pipe(
      delay(1000),
      tap(() => {
        this.userSignal.set(userData);
        localStorage.setItem(this.storageKey, JSON.stringify(userData));
      })
    );
  }

  logout(): void {
    this.userSignal.set(null);
    localStorage.removeItem(this.storageKey);
  }
}
