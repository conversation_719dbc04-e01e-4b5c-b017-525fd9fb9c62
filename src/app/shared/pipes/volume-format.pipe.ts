import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'volumeFormat',
  standalone: true
})
export class VolumeFormatPipe implements PipeTransform {
  transform(value: number): string {
    if (Math.abs(value) >= 1000000) {
      return (value / 1000000).toFixed(2) + 'M';
    }
    if (Math.abs(value) >= 1000) {
      return (value / 1000).toFixed(2) + 'K';
    }
    return value.toString();
  }
}
