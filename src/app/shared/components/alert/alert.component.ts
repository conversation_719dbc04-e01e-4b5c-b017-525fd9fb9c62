import { Component, Input, Output, EventEmitter, booleanAttribute, TemplateRef, ContentChild } from '@angular/core';
import { NgIf, Ng<PERSON>lass, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'app-alert',
  standalone: true,
  imports: [NgIf, NgClass, NgTemplateOutlet],
  templateUrl: './alert.component.html',
  styleUrl: './alert.component.css'
})
export class AlertComponent {
  @Input() type: 'info' | 'success' | 'warning' | 'danger' = 'info';
  @Input() title = '';
  @Input() message = '';
  @Input({ transform: booleanAttribute }) dismissible = false;
  @Input() icon = '';
  @Input({ transform: booleanAttribute }) showIcon = true;
  @Input({ transform: booleanAttribute }) bordered = false;
  @Input({ transform: booleanAttribute }) compact = false;

  @ContentChild('alertFooter') alertFooter?: TemplateRef<unknown>;

  @Output() closed = new EventEmitter<void>();

  visible = true;

  getAlertIcon(): string {
    if (this.icon) return this.icon;

    switch (this.type) {
      case 'success': return '✓';
      case 'warning': return '⚠';
      case 'danger': return '✕';
      case 'info':
      default: return 'ℹ';
    }
  }

  closeAlert() {
    this.visible = false;
    this.closed.emit();
  }
}
