.alert {
  display: flex;
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
  position: relative;
  transition: all 0.3s var(--easing-default);
}

.alert-info {
  background-color: color-mix(in srgb, var(--info-color) 15%, transparent);
  color: var(--info-color);
}

.alert-footer {
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.alert-footer-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  margin-top: 0.5rem;
}

.alert-success {
  background-color: rgba(2, 192, 118, 0.15);
  color: var(--success-color);
}

.alert-warning {
  background-color: rgba(240, 185, 11, 0.15);
  color: var(--warning-color);
}

.alert-danger {
  background-color: rgba(246, 70, 93, 0.15);
  color: var(--danger-color);
}

.alert-bordered {
  border-left: 4px solid;
}

.alert-info.alert-bordered {
  border-left-color: var(--info-color);
}

.alert-success.alert-bordered {
  border-left-color: var(--success-color);
}

.alert-warning.alert-bordered {
  border-left-color: var(--warning-color);
}

.alert-danger.alert-bordered {
  border-left-color: var(--danger-color);
}

.alert-icon {
  margin-right: var(--spacing-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  height: 24px;
  width: 24px;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-xs);
  display: block;
}

.alert-message {
  color: var(--text-primary);
  opacity: 0.9;
}

.alert-close {
  background: transparent;
  border: none;
  cursor: pointer;
  color: currentColor;
  font-size: 1rem;
  padding: 0.3rem;
  opacity: 0.7;
  transition: opacity 0.2s ease;
  margin-left: var(--spacing-sm);
}

.alert-close:hover {
  opacity: 1;
}

.alert-compact {
  padding: var(--spacing-sm);
}

.alert-compact .alert-icon {
  font-size: 1rem;
  height: 20px;
  width: 20px;
}

.alert-compact .alert-title {
  margin-bottom: 0;
  display: inline;
  margin-right: var(--spacing-xs);
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.alert-closing {
  animation: fadeOut 0.3s forwards;
}
