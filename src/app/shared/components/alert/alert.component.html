<div
  *ngIf="visible"
  [ngClass]="[
    'alert',
    'alert-' + type,
    bordered ? 'alert-bordered' : '',
    compact ? 'alert-compact' : ''
  ]">

  <div *ngIf="showIcon" class="alert-icon" role="presentation">
    {{ getAlertIcon() }}
  </div>

  <div class="alert-content">
    <strong *ngIf="title" class="alert-title">{{ title }}</strong>
    <div class="alert-message">
      <ng-container *ngIf="!message">
        <ng-content></ng-content>
      </ng-container>
      {{ message }}
    </div>
  </div>

  <button
    *ngIf="dismissible"
    type="button"
    class="alert-close"
    aria-label="Close alert"
    (click)="closeAlert()">
    ✕
  </button>
  <div *ngIf="alertFooter" class="alert-footer">
    <ng-container [ngTemplateOutlet]="alertFooter"></ng-container>
  </div>
</div>
