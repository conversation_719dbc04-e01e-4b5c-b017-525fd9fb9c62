.card {
  background-color: var(--secondary-color);
  color: var(--text-primary);
  transition: all 0.3s var(--easing-default);
  margin-bottom: var(--spacing-md);
  overflow: hidden;
}

.card-shadow-low {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.card-shadow-medium {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-shadow-high {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.card-radius-default {
  border-radius: var(--border-radius);
}

.card-radius-large {
  border-radius: calc(var(--border-radius) * 2);
}

.card-radius-none {
  border-radius: 0;
}

.card-padding-none {
  padding: 0;
}

.card-padding-small {
  padding: var(--spacing-sm);
}

.card-padding-medium {
  padding: var(--spacing-md);
}

.card-padding-large {
  padding: var(--spacing-lg);
}

.card-hoverable {
  cursor: pointer;
}

.card-hoverable:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.2);
}

.card-header {
  margin-bottom: var(--spacing-md);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  margin: 0;
  padding: 0;
  color: var(--text-primary);
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: var(--spacing-xs) 0 0 0;
  padding: 0;
}

.card-content {
  margin-bottom: var(--spacing-md);
}

.card-footer {
  margin-top: var(--spacing-md);
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: var(--spacing-sm);
}

.card-footer:empty {
  display: none;
}

@media (max-width: var(--breakpoint-sm)) {
  .card-padding-medium {
    padding: var(--spacing-sm);
  }

  .card-padding-large {
    padding: var(--spacing-md);
  }
}
