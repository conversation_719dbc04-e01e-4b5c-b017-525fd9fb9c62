import { Component, Input } from '@angular/core';
import { NgClass, NgIf } from '@angular/common';

@Component({
  selector: 'app-card',
  standalone: true,
  imports: [NgC<PERSON>, NgIf],
  templateUrl: './card.component.html',
  styleUrl: './card.component.css'
})
export class CardComponent {
  @Input() title = '';
  @Input() subtitle = '';
  @Input() elevation: 'low' | 'medium' | 'high' = 'low';
  @Input() hoverable = false;
  @Input() borderRadius: 'default' | 'large' | 'none' = 'default';
  @Input() padding: 'none' | 'small' | 'medium' | 'large' = 'medium';
}
