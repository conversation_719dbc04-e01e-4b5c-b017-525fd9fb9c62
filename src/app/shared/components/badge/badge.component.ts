import { booleanAttribute, Component, Input } from '@angular/core';
import { NgClass, NgIf } from '@angular/common';

@Component({
  selector: 'app-badge',
  standalone: true,
  imports: [NgClass, NgIf],
  templateUrl: './badge.component.html',
  styleUrl: './badge.component.css'
})
export class BadgeComponent {
  @Input() type: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' = 'primary';
  @Input() size: 'sm' | 'md' | 'lg' = 'md';
  @Input({transform: booleanAttribute}) pill = false;
  @Input({transform: booleanAttribute}) outline = false;
  @Input() icon = '';
}
