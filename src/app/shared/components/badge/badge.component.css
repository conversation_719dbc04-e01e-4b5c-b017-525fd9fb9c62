.badge {
  display: inline-flex;
  align-items: center;
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  line-height: 1;
  border-radius: var(--border-radius);
  transition: all 0.2s ease;
}

.badge-pill {
  border-radius: 50px;
}

.badge-sm {
  padding: 0.1rem 0.4rem;
  font-size: 0.7rem;
}

.badge-md {
  padding: 0.25rem 0.6rem;
  font-size: 0.8rem;
}

.badge-lg {
  padding: 0.35rem 0.8rem;
  font-size: 0.9rem;
}

.badge-icon {
  margin-right: 0.35rem;
  display: flex;
  align-items: center;
}

.badge-primary {
  background-color: var(--primary-color);
  color: var(--background);
}

.badge-secondary {
  background-color: var(--secondary-color);
  color: var(--text-primary);
}

.badge-success {
  background-color: var(--success-color);
  color: var(--background);
}

.badge-danger {
  background-color: var(--danger-color);
  color: var(--background);
}

.badge-warning {
  background-color: var(--warning-color);
  color: var(--background);
}

.badge-info {
  background-color: var(--info-color);
  color: var(--background);
}

.badge-outline {
  background-color: transparent;
  border: 1px solid;
}

.badge-outline.badge-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.badge-outline.badge-secondary {
  color: var(--text-secondary);
  border-color: var(--text-secondary);
}

.badge-outline.badge-success {
  color: var(--success-color);
  border-color: var(--success-color);
}

.badge-outline.badge-danger {
  color: var(--danger-color);
  border-color: var(--danger-color);
}

.badge-outline.badge-warning {
  color: var(--warning-color);
  border-color: var(--warning-color);
}

.badge-outline.badge-info {
  color: var(--info-color);
  border-color: var(--info-color);
}
