.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-trigger {
  border: none;
  background: transparent;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  padding: 0.5rem 0.75rem;
  font-family: inherit;
  border-radius: var(--border-radius);
  transition: all 0.2s ease;
  color: var(--text-primary);
}

.dropdown-trigger-primary {
  background-color: var(--primary-color);
  color: var(--background);
}

.dropdown-trigger-primary:hover:not(:disabled) {
  background-color: color-mix(in srgb, var(--primary-color), black 20%);
}

.dropdown-trigger-secondary {
  background-color: var(--secondary-color);
}

.dropdown-trigger-secondary:hover:not(:disabled) {
  background-color: color-mix(in srgb, var(--secondary-color), white 10%);
}

.dropdown-trigger-outline {
  background-color: transparent;
  border: 1px solid var(--text-secondary);
  color: var(--text-primary);
}

.dropdown-trigger-outline:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.05);
}

.dropdown-trigger:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.dropdown-trigger-icon {
  transition: transform 0.2s ease;
}

.dropdown-menu {
  position: absolute;
  min-width: 180px;
  background-color: var(--secondary-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: var(--border-radius);
  z-index: var(--z-layer-3);
  transform-origin: top left;
  overflow: hidden;
  transition: transform 0.15s ease, opacity 0.15s ease;
  opacity: 0;
  transform: scale(0.95);
  pointer-events: none;
}

.dropdown-menu.active {
  opacity: 1;
  transform: scale(1);
  pointer-events: auto;
}

.dropdown-menu-bottom-left {
  top: calc(100% + 0.5rem);
  left: 0;
}

.dropdown-menu-bottom-right {
  top: calc(100% + 0.5rem);
  right: 0;
}

.dropdown-menu-top-left {
  bottom: calc(100% + 0.5rem);
  left: 0;
}

.dropdown-menu-top-right {
  bottom: calc(100% + 0.5rem);
  right: 0;
}

.dropdown-trigger.active .dropdown-trigger-icon {
  transform: rotate(180deg);
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.dropdown-menu.animate-in {
  animation: scaleIn 0.15s forwards;
}

@keyframes scaleOut {
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(0.95);
    opacity: 0;
  }
}

.dropdown-menu.animate-out {
  animation: scaleOut 0.15s forwards;
}

.dropdown-header {
  padding: 0.5rem 1rem;
  font-weight: bold;
  color: var(--text-secondary);
  font-size: 0.875rem;
  cursor: default;
}

.dropdown-divider {
  height: 1px;
  margin: 0.5rem 1rem;
  background-color: var(--border-color);
  border: none;
  opacity: 0.6;
  display: block;
}
