import {
  Component, Input, Output, EventEmitter, ElementRef,
  HostListener, ContentChild, TemplateRef,
  Directive, booleanAttribute
} from '@angular/core';
import { Ng<PERSON><PERSON>, NgIf, NgTemplateOutlet } from '@angular/common';

@Directive({standalone: true, selector: '[appDropdownHeader]'})
export class DropdownHeaderDirective {}


@Component({
  selector: 'app-dropdown',
  standalone: true,
  imports: [NgClass, NgIf, NgTemplateOutlet, DropdownHeaderDirective],
  templateUrl: './dropdown.component.html',
  styleUrl: './dropdown.component.css'
})
export class DropdownComponent {
  @Input() label = '';
  @Input() position: 'bottom-left' | 'bottom-right' | 'top-left' | 'top-right' = 'bottom-left';
  @Input() icon = '';
  @Input() buttonType: 'primary' | 'secondary' | 'outline' = 'secondary';
  @Input() minWidth = '';
  @Input({ transform: booleanAttribute }) closeOnClick = true;
  @Input({ transform: booleanAttribute }) disabled = false;

  @Output() opened = new EventEmitter<void>();
  @Output() closed = new EventEmitter<void>();

  @ContentChild('dropdownTrigger') customTrigger?: TemplateRef<void>;

  isOpen = false;

  constructor(private elementRef: ElementRef) {}

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    if (!this.elementRef.nativeElement.contains(event.target) && this.isOpen) {
      this.close();
    }
  }

  toggle() {
    if (this.disabled) return;
    if (this.isOpen) {
      this.close();
    } else {
      this.open();
    }
  }

  open() {
    this.isOpen = true;
    this.opened.emit();
  }

  close() {
    if (this.isOpen) {
      this.isOpen = false;
      this.closed.emit();
    }
  }
}
