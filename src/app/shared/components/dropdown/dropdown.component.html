<div class="dropdown">
  <ng-container *ngIf="customTrigger; else defaultTrigger">
    <div (click)="toggle()" (keydown.enter)="toggle()" tabindex="0" role="button">
      <ng-container *ngTemplateOutlet="customTrigger"></ng-container>
    </div>
  </ng-container>

  <ng-template #defaultTrigger>
    <button
      [disabled]="disabled"
      [ngClass]="[
        'dropdown-trigger',
        'dropdown-trigger-' + buttonType,
        isOpen ? 'active' : ''
      ]"
      (click)="toggle()">
      <span *ngIf="icon" class="dropdown-trigger-icon-left">{{ icon }}</span>
      <span>{{ label }}</span>
      <span class="dropdown-trigger-icon">▼</span>
    </button>
  </ng-template>

  <div
    [style.min-width]="minWidth || null"
    [ngClass]="[
      'dropdown-menu',
      'dropdown-menu-' + position,
      isOpen ? 'active' : ''
    ]">
    <div class="dropdown-content">
      <ng-content select="[appDropdownHeader]"></ng-content>
      <ng-content select="app-dropdown-item"></ng-content>
    </div>
  </div>
</div>
