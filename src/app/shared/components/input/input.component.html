<div class="input-container" [class.input-error]="hasError">
  <label *ngIf="label" [for]="id" class="input-label">
    {{ label }}
    <span *ngIf="required" class="required-indicator">*</span>
  </label>

  <div class="input-wrapper" [class.focused]="focused" [class.disabled]="disabled">
    <div *ngIf="prefixIcon" class="input-icon input-prefix">
      <span class="icon">{{ prefixIcon }}</span>
    </div>

    <input
      [id]="id"
      [name]="name"
      [type]="type"
      [placeholder]="placeholder"
      [disabled]="disabled"
      [readonly]="readonly"
      [value]="value"
      [attr.maxlength]="maxLength"
      [attr.minlength]="minLength"
      [required]="required"
      [autocomplete]="autocomplete"
      [spellcheck]="spellcheck"
      (input)="onInput($event)"
      (focus)="onFocus()"
      (blur)="onBlur($event)"
      [class]="'input input-' + size"
      [attr.aria-invalid]="hasError"
      [attr.aria-required]="required"
      [attr.aria-describedby]="hasError ? id + '-error' : (helperText ? id + '-helper' : null)"
    />

    <div *ngIf="suffixIcon || (hasClearButton && value)" class="input-icon input-suffix">
      <button
        *ngIf="hasClearButton && value"
        type="button"
        class="clear-button"
        aria-label="Clear input"
        (click)="clearInput()">
        ✕
      </button>
      <span *ngIf="suffixIcon" class="icon">{{ suffixIcon }}</span>
    </div>
  </div>

  <div *ngIf="errorMessage && hasError" [id]="id + '-error'" class="input-error-message" role="alert">
    {{ errorMessage }}
  </div>

  <div *ngIf="helperText && !hasError" [id]="id + '-helper'" class="input-helper-text">
    {{ helperText }}
  </div>

  <div *ngIf="maxLength && value" class="input-character-count">
    {{ value.length }}/{{ maxLength }}
  </div>
</div>
