.input-container {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
  width: 100%;
}

.input-label {
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
  font-weight: 500;
  display: flex;
  align-items: center;
}

.required-indicator {
  color: var(--danger-color);
  margin-left: 0.25rem;
  font-weight: bold;
}

.input-wrapper {
  display: flex;
  align-items: center;
  border-radius: var(--border-radius);
  background-color: var(--secondary-color);
  border: 1px solid transparent;
  transition: all 0.2s ease;
  overflow: hidden;
  position: relative;
}

.input-wrapper.focused {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(240, 185, 11, 0.2);
}

.input-wrapper.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: rgba(0, 0, 0, 0.1);
}

.input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: var(--text-primary);
  width: 100%;
  font-family: inherit;
  transition: all 0.2s ease;
}

.input::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.input:focus::placeholder {
  opacity: 0.5;
}

.input-sm {
  padding: 0.5rem;
  font-size: 0.8rem;
}

.input-md {
  padding: 0.75rem;
  font-size: 1rem;
}

.input-lg {
  padding: 1rem;
  font-size: 1.2rem;
}

.input-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  padding: 0 0.5rem;
}

.input-prefix {
  padding-left: 0.75rem;
}

.input-suffix {
  padding-right: 0.75rem;
}

.input-error .input-wrapper {
  border-color: var(--danger-color);
}

.input-error-message {
  font-size: 0.8rem;
  color: var(--danger-color);
  margin-top: 0.3rem;
  animation: fadeIn 0.2s ease-in-out;
}

.input-helper-text {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-top: 0.3rem;
}

.input-character-count {
  font-size: 0.7rem;
  color: var(--text-secondary);
  margin-top: 0.3rem;
  text-align: right;
  margin-left: auto;
}

.clear-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 0.8rem;
  padding: 0.2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.2rem;
  height: 1.2rem;
  transition: all 0.2s;
}

.clear-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

.input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.input[readonly] {
  background-color: rgba(0, 0, 0, 0.1);
}

.input-wrapper:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(240, 185, 11, 0.2);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: var(--breakpoint-sm)) {
  .input-container {
    margin-bottom: 0.75rem;
  }

  .input-md {
    padding: 0.6rem;
    font-size: 0.95rem;
  }

  .input-lg {
    padding: 0.8rem;
    font-size: 1.1rem;
  }
}
