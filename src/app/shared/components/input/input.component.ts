import { Component, Input, OnInit, forwardRef, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, FormsModule, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-input',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './input.component.html',
  styleUrl: './input.component.css',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => InputComponent),
      multi: true
    }
  ]
})
export class InputComponent implements ControlValueAccessor, OnInit {
  @Input() id = `input-${Math.random().toString(36).substring(2, 11)}`;
  @Input() type: 'text' | 'number' | 'email' | 'password' | 'url' | 'search' | 'tel' | 'date' = 'text';
  @Input() placeholder = '';
  @Input() label = '';
  @Input() size: 'sm' | 'md' | 'lg' = 'md';
  @Input() value = '';
  @Input() disabled = false;
  @Input() readonly = false;
  @Input() hasError = false;
  @Input() errorMessage = '';
  @Input() helperText = '';
  @Input() prefixIcon = '';
  @Input() suffixIcon = '';
  @Input() hasClearButton = false;
  @Input() maxLength?: number;
  @Input() minLength?: number;
  @Input() required = false;
  @Input() autocomplete = 'off';
  @Input() spellcheck = false;
  @Input() name = '';

  @Output() valueChange = new EventEmitter<string>();
  @Output() inputFocus = new EventEmitter<void>();
  @Output() inputBlur = new EventEmitter<FocusEvent>();
  @Output() clear = new EventEmitter<void>();

  focused = false;
  touched = false;

  private onChange: (value: string) => void = () => { /* empty */ };
  private onTouched: () => void = () => { /* empty */ };

  ngOnInit() {
    if (!this.name) {
      this.name = this.id;
    }
  }

  onInput(event: Event) {
    const input = event.target as HTMLInputElement;
    this.value = input.value;
    this.valueChange.emit(this.value);
    this.onChange(this.value);
  }

  onFocus() {
    this.focused = true;
    this.inputFocus.emit();
  }

  onBlur(event: FocusEvent) {
    this.focused = false;
    if (!this.touched) {
      this.touched = true;
      this.onTouched();
    }
    this.inputBlur.emit(event);
  }

  clearInput() {
    this.value = '';
    this.valueChange.emit(this.value);
    this.onChange(this.value);
    this.clear.emit();
  }

  writeValue(value: string): void {
    this.value = value || '';
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }
}
