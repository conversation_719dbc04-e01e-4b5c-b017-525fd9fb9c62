.btn {
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: all 0.2s;
  margin: 0.5rem;
  border: none;
  outline: none;
  position: relative;
  overflow: hidden;
  font-family: inherit;
  cursor: pointer;
}

.btn:focus {
  box-shadow: 0 0 0 3px rgba(240, 185, 11, 0.25);
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.button-text {
  font-weight: inherit;
  transition: opacity 0.2s ease;
}

.button-text.with-icon {
  margin: 0 0.3rem;
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon-left {
  margin-right: 0.5rem;
}

.btn-icon-right {
  margin-left: 0.5rem;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--background);
}

.btn-primary:hover:not(:disabled) {
  background-color: color-mix(in srgb, var(--primary-color), black 20%);
  transform: translateY(-1px);
}

.btn-primary:active:not(:disabled) {
  transform: translateY(0);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--text-primary);
}

.btn-secondary:hover:not(:disabled) {
  background-color: color-mix(in srgb, var(--secondary-color), white 10%);
  transform: translateY(-1px);
}

.btn-secondary:active:not(:disabled) {
  transform: translateY(0);
}

.btn-success {
  background-color: var(--success-color);
  color: var(--background);
}

.btn-success:hover:not(:disabled) {
  background-color: color-mix(in srgb, var(--success-color), black 20%);
  transform: translateY(-1px);
}

.btn-success:active:not(:disabled) {
  transform: translateY(0);
}

.btn-danger {
  background-color: var(--danger-color);
  color: var(--text-primary);
}

.btn-danger:hover:not(:disabled) {
  background-color: color-mix(in srgb, var(--danger-color), black 20%);
  transform: translateY(-1px);
}

.btn-danger:active:not(:disabled) {
  transform: translateY(0);
}

.btn-warning {
  background-color: var(--warning-color);
  color: var(--background);
}

.btn-warning:hover:not(:disabled) {
  background-color: color-mix(in srgb, var(--warning-color), black 20%);
  transform: translateY(-1px);
}

.btn-warning:active:not(:disabled) {
  transform: translateY(0);
}

.btn-info {
  background-color: var(--info-color);
  color: var(--background);
}

.btn-info:hover:not(:disabled) {
  background-color: color-mix(in srgb, var(--info-color), black 20%);
  transform: translateY(-1px);
}

.btn-info:active:not(:disabled) {
  transform: translateY(0);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
  background-color: rgba(240, 185, 11, 0.1);
  transform: translateY(-1px);
}

.btn-outline:active:not(:disabled) {
  transform: translateY(0);
}

.btn-rounded {
  border-radius: 50px;
}

.btn-full-width {
  width: 100%;
  margin-left: 0;
  margin-right: 0;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
}

.btn-md {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1.2rem;
}

.btn-font-normal {
  font-weight: normal;
}

.btn-font-bold {
  font-weight: bolder;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn:disabled:hover {
  transform: none;
}

.spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.btn:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.3);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  100% {
    transform: scale(20, 20);
    opacity: 0;
  }
}

.btn:focus:not(:active)::after {
  animation: ripple 1s ease-out;
}

@media (max-width: var(--breakpoint-sm)) {
  .btn {
    margin: 0.3rem;
  }

  .btn-md {
    padding: 0.6rem 1.2rem;
    font-size: 0.95rem;
  }

  .btn-lg {
    padding: 0.8rem 1.6rem;
    font-size: 1.1rem;
  }
}
