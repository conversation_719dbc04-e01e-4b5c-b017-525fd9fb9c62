<button
  [type]="buttonType"
  [class]="[
    'btn',
    'btn-' + type,
    'btn-' + size,
    'btn-font-' + bold,
    rounded ? 'btn-rounded' : '',
    fullWidth ? 'btn-full-width' : ''
  ]"
  [attr.aria-label]="ariaLabel || null"
  [attr.aria-disabled]="disabled || loading"
  [disabled]="disabled || loading"
  (click)="onClick($event)">

  <div class="btn-content">
    <span *ngIf="icon && iconPosition === 'left'" class="btn-icon btn-icon-left">
      {{ icon }}
    </span>

    <span *ngIf="loading" class="spinner" aria-hidden="true"></span>

    <span *ngIf="!loading" class="button-text" [class.with-icon]="icon">
      <ng-content></ng-content>
    </span>

    <span *ngIf="icon && iconPosition === 'right'" class="btn-icon btn-icon-right">
      {{ icon }}
    </span>
  </div>
</button>
