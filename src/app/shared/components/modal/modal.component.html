<div
  *ngIf="isOpen"
  class="modal-backdrop"
  [class.modal-animation]="animation"
  [class.modal-centered]="centered"
  (click)="onBackdropClick($event)"
  (keydown)="onBackdropClick($event)"
  tabindex="0">

  <div
    class="modal-container"
    [ngClass]="'modal-' + size"
    role="dialog"
    aria-modal="true"
    [attr.aria-labelledby]="title ? 'modal-title' : null"
    tabindex="-1">

    <div class="modal-header">
      <h3 *ngIf="title" id="modal-title" class="modal-title">{{ title }}</h3>
      <button
        *ngIf="closable"
        type="button"
        class="modal-close"
        aria-label="Close"
        (click)="closeModal()"
        (keydown.enter)="closeModal()"
        (keydown.space)="closeModal()"
        tabindex="0">
        ✕
      </button>
    </div>

    <div class="modal-body">
      <ng-content></ng-content>
    </div>

    <div *ngIf="showFooter" class="modal-footer">
      <app-button
        [type]="cancelType"
        (click)="onCancel()">
        {{ cancelText }}
      </app-button>
      <app-button
        [type]="confirmType"
        (click)="onConfirm()">
        {{ confirmText }}
      </app-button>
    </div>
  </div>
</div>
