.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  z-index: var(--z-layer-modal);
  overflow-y: auto;
  padding: 1.5rem;
}

.modal-centered {
  align-items: center;
  justify-content: center;
}

.modal-container {
  background-color: var(--background);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  margin: auto;
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 3rem);
  width: 100%;
}

.modal-sm {
  max-width: 320px;
}

.modal-md {
  max-width: 480px;
}

.modal-lg {
  max-width: 720px;
}

.modal-xl {
  max-width: 960px;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.25rem;
  border-bottom: 1px solid var(--secondary-color);
}

.modal-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  line-height: 1;
}

.modal-close:hover {
  color: var(--text-primary);
}

.modal-body {
  padding: 1.25rem;
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  padding: 1rem 1.25rem;
  border-top: 1px solid var(--secondary-color);
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

.modal-animation {
  animation: modalFadeIn 0.2s ease-out;
}

.modal-animation .modal-container {
  animation: modalSlideIn 0.2s ease-out;
}

@keyframes modalFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes modalSlideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

body.modal-open {
  overflow: hidden;
}
