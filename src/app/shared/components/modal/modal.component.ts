import { Component, Input, Output, EventEmitter, HostListener, booleanAttribute } from '@angular/core';
import { NgClass, NgIf } from '@angular/common';
import { ButtonComponent } from '../button/button.component';

@Component({
  selector: 'app-modal',
  standalone: true,
  imports: [NgClass, NgIf, ButtonComponent],
  templateUrl: './modal.component.html',
  styleUrl: './modal.component.css'
})
export class ModalComponent {
  @Input() title = '';
  @Input() size: 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input({transform: booleanAttribute}) centered = false;
  @Input({transform: booleanAttribute}) closable = true;
  @Input({transform: booleanAttribute}) backdrop = true;
  @Input({transform: booleanAttribute}) escClose = true;
  @Input({transform: booleanAttribute}) animation = true;
  @Input({transform: booleanAttribute}) showFooter = true;
  @Input() confirmText = 'Confirm';
  @Input() cancelText = 'Cancel';
  @Input() confirmType: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' = 'primary';
  @Input() cancelType: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'outline' = 'outline';

  @Output() confirm = new EventEmitter<void>();
  @Output() cancelClick = new EventEmitter<void>();
  @Output() backdropClicked = new EventEmitter<void>();
  @Output() modalClosed = new EventEmitter<void>();

  private _isOpen = true;

  @Input()
  set isOpen(value: boolean) {
    this._isOpen = value;
    if (value) {
      document.body.classList.add('modal-open');
    } else {
      document.body.classList.remove('modal-open');
    }
  }

  get isOpen(): boolean {
    return this._isOpen;
  }

  @HostListener('document:keydown.escape')
  onEscapeKey() {
    if (this.isOpen && this.escClose) {
      this.closeModal();
    }
  }

  onBackdropClick(event: Event) {
    if (event.target === event.currentTarget && this.backdrop) {
      this.backdropClicked.emit();
      if (this.closable) {
        this.closeModal();
      }
    }
  }

  onConfirm() {
    this.confirm.emit();
  }

  onCancel() {
    this.cancelClick.emit();
    this.closeModal();
  }

  closeModal() {
    this.isOpen = false;
    this.modalClosed.emit();
  }
}
