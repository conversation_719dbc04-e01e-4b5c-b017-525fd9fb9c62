.toast {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-width: 250px;
  max-width: 350px;
  background-color: var(--secondary-color);
  color: var(--text-primary);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  margin: var(--spacing-sm);
  position: fixed;
  z-index: var(--z-layer-toast);
  overflow: hidden;
  animation: toastIn 0.3s var(--easing-default);
  transition: all 0.3s var(--easing-default);
  border-left: 3px solid;
}

.toast-position-top-right {
  top: var(--spacing-md);
  right: var(--spacing-md);
}

.toast-position-top-left {
  top: var(--spacing-md);
  left: var(--spacing-md);
}

.toast-position-bottom-right {
  bottom: var(--spacing-md);
  right: var(--spacing-md);
}

.toast-position-bottom-left {
  bottom: var(--spacing-md);
  left: var(--spacing-md);
}

.toast-position-top-center {
  top: var(--spacing-md);
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto;
}

.toast-position-bottom-center {
  bottom: var(--spacing-md);
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto;
}

.toast-success {
  border-left-color: var(--success-color);
}

.toast-error {
  border-left-color: var(--danger-color);
}

.toast-warning {
  border-left-color: var(--warning-color);
}

.toast-info {
  border-left-color: var(--info-color);
}

.toast-content {
  display: flex;
  padding: var(--spacing-md);
  align-items: center;
}

.toast-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: var(--spacing-sm);
  font-weight: bold;
  flex-shrink: 0;
}

.toast-success .toast-icon {
  background-color: color-mix(in srgb, var(--success-color), 50% black);
  color: var(--success-color);
}

.toast-error .toast-icon {
  background-color: color-mix(in srgb, var(--danger-color), 50% black);
  color: var(--danger-color);
}

.toast-warning .toast-icon {
  background-color: color-mix(in srgb, var(--warning-color), 50% black);
  color: var(--warning-color);
}

.toast-info .toast-icon {
  background-color: color-mix(in srgb, var(--info-color), 50% black);
  color: var(--info-color);
}

.toast-body {
  flex: 1;
}

.toast-title {
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-xs);
}

.toast-message {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.toast-close {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: var(--font-size-md);
  padding: 0;
  margin-left: var(--spacing-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.toast-close:hover {
  color: var(--text-primary);
}

.toast-progress-bar {
  height: 4px;
  background-color: color-mix(in srgb, var(--primary-color), 30% black);
  width: 100%;
}

.toast-progress {
  height: 100%;
  width: 100%;
  transform-origin: left;
  animation: progress-bar 5s linear forwards;
}

.toast-success .toast-progress {
  background-color: var(--success-color);
}

.toast-error .toast-progress {
  background-color: var(--danger-color);
}

.toast-warning .toast-progress {
  background-color: var(--warning-color);
}

.toast-info .toast-progress {
  background-color: var(--info-color);
}

@keyframes toastIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes progress-bar {
  0% {
    transform: scaleX(1);
  }
  100% {
    transform: scaleX(0);
  }
}

@media (max-width: var(--breakpoint-sm)) {
  .toast {
    max-width: calc(100vw - var(--spacing-md) * 2);
    margin: var(--spacing-xs);
  }

  .toast-position-top-left,
  .toast-position-top-right,
  .toast-position-top-center {
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    left: var(--spacing-sm);
    transform: none;
    width: calc(100% - var(--spacing-sm) * 2);
    max-width: none;
  }

  .toast-position-bottom-left,
  .toast-position-bottom-right,
  .toast-position-bottom-center {
    bottom: var(--spacing-sm);
    right: var(--spacing-sm);
    left: var(--spacing-sm);
    transform: none;
    width: calc(100% - var(--spacing-sm) * 2);
    max-width: none;
  }
}
