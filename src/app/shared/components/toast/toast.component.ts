import { Component, Input, Output, EventEmitter, TemplateRef, ContentChild, OnInit } from '@angular/core';
import { Ng<PERSON><PERSON>, NgI<PERSON>, Ng<PERSON>tyle, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'app-toast',
  standalone: true,
  imports: [
    Ng<PERSON>lass,
    NgIf,
    NgTemplateOutlet,
    NgStyle
  ],
  templateUrl: './toast.component.html',
  styleUrl: './toast.component.css'
})
export class ToastComponent implements OnInit{
  @Input() type: 'success' | 'error' | 'warning' | 'info' = 'info';
  @Input() title = '';
  @Input() message = '';
  @Input() autoClose = true;
  @Input() duration = 5000;
  @Input() position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center' = 'top-right';
  @Input() showIcon = true;
  @Input() showProgress = true;
  @Input() closable = true;

  @Output() closed = new EventEmitter<void>();

  @ContentChild('customTemplate') customTemplate?: TemplateRef<unknown>;

  icons = {
    success: '✓',
    error: '✕',
    warning: '⚠',
    info: 'ℹ'
  };

  progressStyle: Record<string, string> = {};
  visible = true;
  timeoutId: number | null = null;

  ngOnInit() {
    if (this.autoClose) {
      this.startCloseTimer();
    }
  }

  startCloseTimer() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }

    if (this.showProgress) {
      this.animateProgress();
    }

    this.timeoutId = window.setTimeout(() => {
      this.close();
    }, this.duration);
  }

  pauseCloseTimer() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }

  animateProgress() {
    this.progressStyle = {
      'animation-duration': `${this.duration}ms`
    };
  }

  close() {
    this.visible = false;
    setTimeout(() => {
      this.closed.emit();
    }, 300); // Match the CSS transition duration
  }
}
