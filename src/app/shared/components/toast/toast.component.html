<div
  *ngIf="visible"
  class="toast"
  [ngClass]="[
    'toast-' + type,
    'toast-position-' + position
  ]"
  (mouseenter)="pauseCloseTimer()"
  (mouseleave)="autoClose && startCloseTimer()">

  <ng-container *ngIf="customTemplate; else defaultTemplate">
    <ng-container *ngTemplateOutlet="customTemplate"></ng-container>
  </ng-container>

  <ng-template #defaultTemplate>
    <div class="toast-content">
      <div *ngIf="showIcon" class="toast-icon">
        {{ icons[type] }}
      </div>

      <div class="toast-body">
        <div *ngIf="title" class="toast-title">{{ title }}</div>
        <div *ngIf="message" class="toast-message">{{ message }}</div>
      </div>

      <button
        *ngIf="closable"
        class="toast-close"
        (click)="close()"
        aria-label="Close toast">
        ✕
      </button>
    </div>

    <div *ngIf="showProgress && autoClose" class="toast-progress-bar">
      <div class="toast-progress" [ngStyle]="progressStyle"></div>
    </div>
  </ng-template>
</div>
