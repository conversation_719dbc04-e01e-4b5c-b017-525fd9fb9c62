import { Component, Input, Output, EventEmitter, booleanAttribute } from '@angular/core';
import { NgClass, NgIf } from '@angular/common';

@Component({
  selector: 'app-dropdown-item',
  standalone: true,
  imports: [NgClass, NgIf],
  templateUrl: './dropdown-item.component.html',
  styleUrl: './dropdown-item.component.css'
})
export class DropdownItemComponent {
  @Input() icon = '';
  @Input({ transform: booleanAttribute }) disabled = false;
  @Input({ transform: booleanAttribute }) active = false;
  @Input({ transform: booleanAttribute }) danger = false;

  @Output() clicked = new EventEmitter<Event>();

  onClick(event: Event) {
    if (!this.disabled) {
      this.clicked.emit(event);
    }
  }
}
