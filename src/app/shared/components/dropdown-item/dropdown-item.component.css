.dropdown-item {
  padding: 0.65rem 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  color: var(--text-primary);
  transition: background-color 0.15s ease;
  user-select: none;
}

.dropdown-item:hover:not(.disabled) {
  background-color: rgba(255, 255, 255, 0.05);
}

.dropdown-item.active {
  background-color: rgba(240, 185, 11, 0.1);
  color: var(--primary-color);
}

.dropdown-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.dropdown-item.danger {
  color: var(--danger-color);
}

.dropdown-item-icon {
  margin-right: 0.5rem;
  opacity: 0.9;
  display: flex;
  align-items: center;
}

.theme-light .dropdown-item:not(.dropdown-divider):hover:not(.disabled) {
  background-color: rgba(0, 0, 0, 0.05);
}

.theme-light .dropdown-divider {
  background-color: rgba(0, 0, 0, 0.1);
}
