* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: '<PERSON>o', sans-serif;
}

body, .theme-dark, .theme-light {
  background-color: var(--background);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

a {
  color: var(--primary-color);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

button {
  cursor: pointer;
  border: none;
  outline: none;
}

.tooltip {
  z-index: 1000;
  border-radius: var(--border-radius);
  max-width: 200px;
  background-color: color-mix(in srgb, var(--text-primary), transparent 10%);
  color: var(--secondary-color);
  transition: opacity 0.2s ease, visibility 0.2s ease;
}
