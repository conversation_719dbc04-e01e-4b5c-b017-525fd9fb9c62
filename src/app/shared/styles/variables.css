:root {
  --background: #181a20;
  --primary-color: #f0b90b;
  --secondary-color: #1e2026;
  --text-primary: #eaecef;
  --text-secondary: #848e9c;
  --success-color: #02c076;
  --danger-color: #f6465d;
  --warning-color: #f0b90b;
  --info-color: #1793ff;

  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;

  --border-radius: 4px;
  --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

  --transition-fast: 0.15s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;
  --easing-default: cubic-bezier(0.4, 0, 0.2, 1);
  --easing-in: cubic-bezier(0.4, 0, 1, 1);
  --easing-out: cubic-bezier(0, 0, 0.2, 1);

  --z-layer-1: 10;
  --z-layer-2: 20;
  --z-layer-3: 30;
  --z-layer-4: 40;
  --z-layer-5: 50;
  --z-layer-top: 100;
  --z-layer-modal: 1000;
  --z-layer-toast: 9999;

  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-md: 1rem;
  --font-size-lg: 1.25rem;
  --font-size-xl: 1.5rem;
  --font-size-2xl: 2rem;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-loose: 1.75;
}

.theme-dark {
  --background: #181a20;
  --secondary-color: #1e2026;
  --text-primary: #eaecef;
  --text-secondary: #848e9c;
}

.theme-light {
  --background: #ffffff;
  --secondary-color: #f5f5f5;
  --text-primary: #212529;
  --text-secondary: #6c757d;
}
