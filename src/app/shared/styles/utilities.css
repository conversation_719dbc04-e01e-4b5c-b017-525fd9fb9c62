.d-flex {
  display: flex;
}

.flex-row {
  flex-direction: row;
}

.flex-column {
  flex-direction: column;
}

.justify-center {
  justify-content: center;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.align-center {
  align-items: center;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.d-grid {
  display: grid;
}

.grid-gap-1 {
  gap: 0.25rem;
}

.grid-gap-2 {
  gap: 0.5rem;
}

.grid-gap-3 {
  gap: 1rem;
}

.grid-gap-4 {
  gap: 1.5rem;
}

.grid-col-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-col-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-col-4 {
  grid-template-columns: repeat(4, 1fr);
}

.m-0 {
  margin: 0;
}

.m-1 {
  margin: 0.25rem;
}

.m-2 {
  margin: 0.5rem;
}

.m-3 {
  margin: 1rem;
}

.m-4 {
  margin: 1.5rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 1rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 1rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-3 {
  margin-left: 1rem;
}

.ml-4 {
  margin-left: 1.5rem;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-3 {
  margin-right: 1rem;
}

.mr-4 {
  margin-right: 1.5rem;
}

.p-0 {
  padding: 0;
}

.p-1 {
  padding: 0.25rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 1rem;
}

.p-4 {
  padding: 1.5rem;
}

.pt-1 {
  padding-top: 0.25rem;
}

.pt-2 {
  padding-top: 0.5rem;
}

.pt-3 {
  padding-top: 1rem;
}

.pt-4 {
  padding-top: 1.5rem;
}

.pb-1 {
  padding-bottom: 0.25rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-3 {
  padding-bottom: 1rem;
}

.pb-4 {
  padding-bottom: 1.5rem;
}

.pl-1 {
  padding-left: 0.25rem;
}

.pl-2 {
  padding-left: 0.5rem;
}

.pl-3 {
  padding-left: 1rem;
}

.pl-4 {
  padding-left: 1.5rem;
}

.pr-1 {
  padding-right: 0.25rem;
}

.pr-2 {
  padding-right: 0.5rem;
}

.pr-3 {
  padding-right: 1rem;
}

.pr-4 {
  padding-right: 1.5rem;
}

.d-none {
  display: none;
}

.d-block {
  display: block;
}

.d-inline {
  display: inline;
}

.d-inline-block {
  display: inline-block;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: var(--primary-color);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-success {
  color: var(--success-color);
}

.text-danger {
  color: var(--danger-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-info {
  color: var(--info-color);
}

.font-bold {
  font-weight: 700;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-light {
  font-weight: 300;
}

.font-sm {
  font-size: 0.8rem;
}

.font-md {
  font-size: 1rem;
}

.font-lg {
  font-size: 1.2rem;
}

.font-xl {
  font-size: 1.5rem;
}

.border {
  border: 1px solid var(--secondary-color);
}

.border-top {
  border-top: 1px solid var(--secondary-color);
}

.border-bottom {
  border-bottom: 1px solid var(--secondary-color);
}

.border-left {
  border-left: 1px solid var(--secondary-color);
}

.border-right {
  border-right: 1px solid var(--secondary-color);
}

.rounded {
  border-radius: var(--border-radius);
}

.rounded-full {
  border-radius: 9999px;
}

/* Responsive utilities */
@media (min-width: var(--breakpoint-sm)) {
  .d-sm-none {
    display: none;
  }

  .d-sm-block {
    display: block;
  }

  .d-sm-flex {
    display: flex;
  }

  .d-sm-grid {
    display: grid;
  }
}

@media (min-width: var(--breakpoint-md)) {
  .d-md-none {
    display: none;
  }

  .d-md-block {
    display: block;
  }

  .d-md-flex {
    display: flex;
  }

  .d-md-grid {
    display: grid;
  }

  .grid-md-col-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-md-col-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .grid-md-col-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: var(--breakpoint-lg)) {
  .d-lg-none {
    display: none;
  }

  .d-lg-block {
    display: block;
  }

  .d-lg-flex {
    display: flex;
  }

  .d-lg-grid {
    display: grid;
  }

  .grid-lg-col-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-lg-col-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .grid-lg-col-4 {
    grid-template-columns: repeat(4, 1fr);
  }

  .grid-lg-col-6 {
    grid-template-columns: repeat(6, 1fr);
  }
}

@media (min-width: var(--breakpoint-xl)) {
  .d-xl-none {
    display: none;
  }

  .d-xl-block {
    display: block;
  }

  .d-xl-flex {
    display: flex;
  }

  .d-xl-grid {
    display: grid;
  }

  .grid-xl-col-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-xl-col-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .grid-xl-col-4 {
    grid-template-columns: repeat(4, 1fr);
  }

  .grid-xl-col-6 {
    grid-template-columns: repeat(6, 1fr);
  }
}

.transition {
  transition: all 0.2s ease;
}

.transition-slow {
  transition: all 0.3s ease;
}

.transition-fast {
  transition: all 0.1s ease;
}

.hover-opacity:hover {
  opacity: 0.8;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.shadow-sm {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.shadow {
  box-shadow: var(--box-shadow);
}

.shadow-lg {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.cursor-pointer {
  cursor: pointer;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}

.position-relative {
  position: relative;
}

.position-absolute {
  position: absolute;
}

.position-fixed {
  position: fixed;
}

.top-0 {
  top: 0;
}

.left-0 {
  left: 0;
}

.right-0 {
  right: 0;
}

.bottom-0 {
  bottom: 0;
}

.z-1 {
  z-index: 1;
}

.z-10 {
  z-index: 10;
}

.z-100 {
  z-index: 100;
}

.z-1000 {
  z-index: 1000;
}
