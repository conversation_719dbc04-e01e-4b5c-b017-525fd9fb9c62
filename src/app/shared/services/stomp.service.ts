import { Injectable, OnD<PERSON>roy } from '@angular/core';
import { RxStomp } from '@stomp/rx-stomp';
import { RxStompConfig } from '@stomp/rx-stomp';
import { StompHeaders } from '@stomp/stompjs';
import { BehaviorSubject, Observable, Subject, Subscription, of } from 'rxjs';
import { filter, map, takeUntil, catchError } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class StompService implements OnDestroy {
  private rxStomp: RxStomp;
  private connectionStatus = new BehaviorSubject<boolean>(false);
  private destroy$ = new Subject<void>();
  private subscriptions: Subscription[] = [];
  private connectionAttempts = 0;
  private readonly MAX_CONNECTION_ATTEMPTS = 2;

  constructor() {
    this.rxStomp = new RxStomp();
    this.configure();
    this.activateWithRetry();
  }

  private configure(): void {
    const stompConfig: RxStompConfig = {
      brokerURL: environment.wsBaseUrl,
      connectionTimeout: 5000, // 5 seconds timeout
      reconnectDelay: 5000, // 5 seconds delay between attempts
      heartbeatIncoming: 0,
      heartbeatOutgoing: 20000,
      // Debug can be useful during development
      debug: (msg: string): void => {
        console.log('STOMP debug:', msg);
      }
    };

    this.rxStomp.configure(stompConfig);
  }

  private activateWithRetry(): void {
    try {
      console.log(`Attempting to connect to WebSocket (attempt ${this.connectionAttempts + 1}/${this.MAX_CONNECTION_ATTEMPTS})`);

      this.subscriptions.push(
        this.rxStomp.connected$.subscribe(() => {
          console.log('STOMP connection established successfully');
          this.connectionStatus.next(true);
          this.connectionAttempts = 0; // Reset counter on successful connection
        }),

        this.rxStomp.stompErrors$.subscribe(error => {
          console.error('STOMP protocol error:', error);
        })
      );

      // Handle connection errors
      this.subscriptions.push(
        this.rxStomp.webSocketErrors$.subscribe(error => {
          console.error('WebSocket connection error:', error);
          this.connectionStatus.next(false);

          if (this.connectionAttempts < this.MAX_CONNECTION_ATTEMPTS) {
            this.connectionAttempts++;
            console.log(`WebSocket connection failed. Will retry in 5 seconds...`);
          } else {
            console.warn(`Failed to connect after ${this.MAX_CONNECTION_ATTEMPTS} attempts. Giving up.`);
          }
        })
      );

      this.rxStomp.activate();
    } catch (error) {
      console.error('Error activating STOMP connection:', error);
      this.connectionStatus.next(false);
    }
  }

  activate(): void {
    this.activateWithRetry();
  }

  deactivate(): void {
    if (this.rxStomp.connected()) {
      this.rxStomp.deactivate();
    }
    this.connectionStatus.next(false);
  }

  isConnected(): Observable<boolean> {
    return this.connectionStatus.asObservable();
  }

  /**
   * Subscribe to a destination and get messages as an observable
   */
  subscribe<T>(destination: string): Observable<T> {
    // Fix: calling the connected() function instead of treating it as a property
    if (!this.rxStomp.connected()) {
      console.warn(`STOMP not connected. Unable to subscribe to ${destination}`);
      return of([] as unknown as T);
    }

    console.log(`Subscribing to ${destination}`);
    return this.rxStomp.watch(destination).pipe(
      map(message => {
        try {
          const parsedBody = JSON.parse(message.body);
          console.log(`Received message from ${destination}:`, parsedBody);
          return parsedBody;
        } catch (e) {
          console.error('Error parsing message body', e);
          return message.body as unknown as T;
        }
      }),
      catchError(error => {
        console.error(`Error subscribing to ${destination}:`, error);
        return of([] as unknown as T);
      }),
      takeUntil(this.destroy$)
    );
  }

  /**
   * Send a message to the specified destination
   */
  publish(destination: string, body: any = {}, headers: StompHeaders = {}): void {
    // Fix: calling the connected() function instead of treating it as a property
    if (!this.rxStomp.connected()) {
      console.warn(`STOMP not connected. Unable to publish to ${destination}`);
      return;
    }

    console.log(`Sending message to ${destination}:`, body);
    this.rxStomp.publish({
      destination,
      body: JSON.stringify(body),
      headers
    });
  }

  /**
   * Send a message to a @MessageMapping endpoint
   */
  send(destination: string, body: any = {}, headers: StompHeaders = {}): void {
    // For Spring's @MessageMapping endpoints, prepend /app if it's not already there
    const appPrefix = environment.wsBaseDestination || '/app';
    const fullDestination = destination.startsWith(appPrefix) ?
      destination :
      `${appPrefix}${destination.startsWith('/') ? '' : '/'}${destination}`;

    this.publish(fullDestination, body, headers);
  }

  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => {
      if (sub && typeof sub.unsubscribe === 'function') {
        sub.unsubscribe();
      }
    });

    this.destroy$.next();
    this.destroy$.complete();

    if (this.rxStomp.active) {
      this.deactivate();
    }
  }
}
