import { Injectable, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { filter, map } from 'rxjs/operators';

export interface WebSocketMessage {
  type: string;
  data: any;
}

@Injectable({
  providedIn: 'root'
})
export class WebSocketService {
  private socket: WebSocket | null = null;
  private readonly destroyRef = inject(DestroyRef);
  private readonly connected = new BehaviorSubject<boolean>(false);
  private readonly messages = new Subject<WebSocketMessage>();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 2000;
  private reconnectTimeoutId: number | null = null;

  connect(url: string): Observable<boolean> {
    if (this.socket) {
      this.socket.close();
    }

    this.initializeSocket(url);

    return this.connected.asObservable().pipe(
      takeUntilDestroyed(this.destroyRef)
    );
  }

  private initializeSocket(url: string): void {
    try {
      console.log(`Attempting to connect to WebSocket at: ${url}`);
      this.socket = new WebSocket(url);

      this.socket.onopen = () => {
        console.log('WebSocket connection established successfully');
        this.connected.next(true);
        this.reconnectAttempts = 0;
      };

      this.socket.onmessage = (event) => {
        try {
          console.log('WebSocket message received:', event.data);
          let message: WebSocketMessage;

          // Check if it's a STOMP message from Spring WebSocket
          if (typeof event.data === 'string' && event.data.startsWith('MESSAGE')) {
            // Parse STOMP frame
            const lines = event.data.split('\n');
            let destination = '';
            let body = '';
            let inBody = false;

            for (let i = 0; i < lines.length; i++) {
              if (inBody) {
                body += lines[i] + (i < lines.length - 2 ? '\n' : '');
              } else if (lines[i].startsWith('destination:')) {
                destination = lines[i].substring('destination:'.length).trim();
              } else if (lines[i] === '') {
                inBody = true; // Body starts after first empty line
              }
            }

            // For STOMP subscription responses, convert to expected format
            try {
              const parsedBody = JSON.parse(body);
              message = {
                type: destination.includes('/users/me/orders') ? 'orders' : 'unknown',
                data: parsedBody
              };
            } catch (e) {
              message = {
                type: 'unknown',
                data: body
              };
            }
          } else {
            // Handle standard JSON messages
            try {
              message = JSON.parse(event.data) as WebSocketMessage;
            } catch (error) {
              console.error('Error parsing WebSocket message:', error);
              message = {
                type: 'unknown',
                data: event.data
              };
            }
          }

          this.messages.next(message);
        } catch (error) {
          console.error('Error processing WebSocket message:', error);
        }
      };

      this.socket.onerror = (error) => {
        console.error('WebSocket error:', error);
      };

      this.socket.onclose = (event) => {
        console.log(`WebSocket connection closed: ${event.code} ${event.reason}`);
        this.connected.next(false);

        // Attempt to reconnect if the connection was not closed intentionally
        if (!event.wasClean) {
          this.attemptReconnect(url);
        }
      };
    } catch (error) {
      console.error('Error creating WebSocket:', error);
      this.attemptReconnect(url);
    }
  }

  private attemptReconnect(url: string): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;

      // Exponential backoff for reconnect
      const delay = Math.min(30000, this.reconnectDelay * Math.pow(1.5, this.reconnectAttempts - 1));

      console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

      if (this.reconnectTimeoutId) {
        window.clearTimeout(this.reconnectTimeoutId);
      }

      this.reconnectTimeoutId = window.setTimeout(() => {
        console.log(`Reconnecting to WebSocket (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
        this.initializeSocket(url);
      }, delay);
    } else {
      console.error(`Failed to reconnect after ${this.maxReconnectAttempts} attempts`);
    }
  }

  // Disconnect from WebSocket server
  disconnect(): void {
    if (this.reconnectTimeoutId) {
      window.clearTimeout(this.reconnectTimeoutId);
      this.reconnectTimeoutId = null;
    }

    if (this.socket) {
      this.socket.close(1000, 'Client disconnecting intentionally');
      this.socket = null;
      this.connected.next(false);
    }
  }

  // Send message to WebSocket server
  send(message: any): void {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(message));
    } else {
      console.error('WebSocket is not connected');
    }
  }

  // Get connection status
  isConnected(): Observable<boolean> {
    return this.connected.asObservable();
  }

  // Get all messages
  getMessages(): Observable<WebSocketMessage> {
    return this.messages.asObservable();
  }

  // Get messages by type
  getMessagesByType(type: string): Observable<any> {
    return this.messages.pipe(
      filter(msg => msg.type === type),
      map(msg => msg.data),
      takeUntilDestroyed(this.destroyRef)
    );
  }
}
