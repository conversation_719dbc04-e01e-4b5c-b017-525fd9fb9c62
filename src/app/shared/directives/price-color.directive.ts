import { Directive, ElementRef, Input, numberAttribute, OnChanges, SimpleChanges } from '@angular/core';

@Directive({
  selector: '[appPriceColor]',
  standalone: true
})
export class PriceColorDirective implements OnChanges {
  @Input({transform: numberAttribute}) appPriceColor = 0;

  constructor(private el: ElementRef) {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['appPriceColor']) {
      this.updateColor();
    }
  }

  private updateColor(): void {
    this.el.nativeElement.style.color = this.appPriceColor >= 0 ? 'var(--success-color)' : 'var(--danger-color)';

    const icon = this.appPriceColor >= 0 ? '↑' : '↓';
    this.el.nativeElement.textContent = `${icon} ${Math.abs(this.appPriceColor)}%`;
  }
}
