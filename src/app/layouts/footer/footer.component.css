.footer {
  background: var(--secondary-color);
  color: var(--text-secondary);
  padding: 3rem 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  border-top: 1px solid var(--border-color);
}

.footer-section {
  display: flex;
  flex-direction: column;
}

.footer h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.footer h4 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1rem;
}

.footer-description {
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.footer ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer li {
  margin-bottom: 0.75rem;
}

.footer a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer a:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

.footer-social {
  display: flex;
  gap: 1rem;
  margin-top: auto;
}

.social-icon {
  color: var(--primary-color);
  font-weight: 500;
}

@media (max-width: 768px) {
  .footer {
    grid-template-columns: 1fr 1fr;
    padding: 2rem 1rem;
  }
}

@media (max-width: 480px) {
  .footer {
    grid-template-columns: 1fr;
  }
}
