.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  position: relative;
}

.navbar-left {
  display: flex;
  align-items: center;
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
  text-decoration: none;
  margin-right: 2rem;
}

.nav-links {
  display: flex;
  gap: 1.5rem;
}

.nav-link {
  text-decoration: none;
}

.navbar-right {
  display: flex;
  gap: 0.75rem;
}

.mobile-only {
  display: none;
}

.mobile-menu-toggle {
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
}

.mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--background);
  z-index: 100;
  padding: 1rem;
  display: none;
  flex-direction: column;
}

.mobile-menu.active {
  display: flex;
}

.mobile-nav-links {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.mobile-nav-link {
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 0;
}

.mobile-auth-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
}

.mobile-button {
  width: 100%;
}

@media (max-width: 768px) {
  .desktop-nav {
    display: none;
  }

  .mobile-only {
    display: block;
  }
}
