<nav class="navbar">
  <div class="navbar-left">
    <a routerLink="/" class="logo">CTM</a>

    <div class="nav-links desktop-nav" *ngIf="isAuthenticated">
      <a routerLink="/markets" routerLinkActive="active" class="nav-link">Markets</a>
      <a routerLink="/trade" routerLinkActive="active" class="nav-link">Trade</a>
      <a routerLink="/wallet" routerLinkActive="active" class="nav-link">Wallet</a>
      <a routerLink="/portfolio" routerLinkActive="active" class="nav-link">Portfolio</a>
    </div>
  </div>

  <div class="navbar-right desktop-nav">
    @if (!isAuthenticated) {
      <app-button (click)="login()" type="outline" size="sm">Login</app-button>
      <app-button (click)="signup()" type="primary" size="sm">Sign Up</app-button>
    } @else {
      <app-button class="font-sm" (click)="logout()" type="outline" size="sm" icon="➟">Logout</app-button>
    }
  </div>

  <div class="mobile-only">
    <app-button type="outline" size="sm" (clicked)="toggleMobileMenu($event)">
      ☰
    </app-button>
  </div>

  <div #mobileMenu class="mobile-menu" [class.active]="isMobileMenuOpen">
    <div class="mobile-nav-links">
      @if (isAuthenticated) {
        <a routerLink="/markets" routerLinkActive="active" class="mobile-nav-link" (click)="closeMobileMenuIfOpen()">Markets</a>
        <a routerLink="/trade" routerLinkActive="active" class="mobile-nav-link" (click)="closeMobileMenuIfOpen()">Trade</a>
        <a routerLink="/wallet" routerLinkActive="active" class="mobile-nav-link" (click)="closeMobileMenuIfOpen()">Wallet</a>
        <a routerLink="/portfolio" routerLinkActive="active" class="mobile-nav-link" (click)="closeMobileMenuIfOpen()">Portfolio</a>
      }

      <div class="mobile-auth-buttons">
        @if (!isAuthenticated) {
          <app-button (click)="login()" type="outline" size="sm" icon="🔒" class="mobile-button">Login</app-button>
          <app-button (click)="signup()" type="primary" size="sm" class="mobile-button">Sign Up</app-button>
        } @else {
          <app-button class="font-sm mobile-button" (click)="logout()" type="outline" size="sm" icon="➟">Logout</app-button>
        }
      </div>
    </div>
  </div>
</nav>
