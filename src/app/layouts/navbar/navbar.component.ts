import { CommonModule } from '@angular/common';
import { ButtonComponent } from '../../shared/components/button/button.component';
import { BadgeComponent } from '../../shared/components/badge/badge.component';
import { RouterLink, RouterLinkActive } from '@angular/router';
import {
  Component,
  HostListener,
  OnInit,
  ChangeDetectorRef,
  ElementRef,
  ViewChild
} from '@angular/core';
import { DropdownComponent, DropdownHeaderDirective } from '../../shared/components/dropdown/dropdown.component';
import { DropdownItemComponent } from '../../shared/components/dropdown-item/dropdown-item.component';
import { MatIcon } from '@angular/material/icon';
import { Router } from '@angular/router';
import {AuthService} from '../../core/services/auth-service/auth.service';

@Component({
  selector: 'app-navbar',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    BadgeComponent,
    RouterLink,
    RouterLinkActive,
    DropdownComponent,
    DropdownHeaderDirective,
    DropdownItemComponent,
    MatIcon
  ],
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.css']
})
export class NavbarComponent implements OnInit {
  isAuthenticated = false;
  isMobileMenuOpen = false;
  @ViewChild('mobileMenuToggle', { static: false }) menuToggleRef!: ElementRef;
  @ViewChild('mobileMenu', { static: false }) mobileMenuRef!: ElementRef;

  constructor(
    private authService: AuthService,
    private cdr: ChangeDetectorRef,
    private router: Router
  ) {}

  ngOnInit() {
    this.isAuthenticated = this.authService.isAuthenticated();
    this.cdr.detectChanges();
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    if (window.innerWidth > 768 && this.isMobileMenuOpen) {
      this.isMobileMenuOpen = false;
      this.cdr.detectChanges();
    }
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    if (!this.isMobileMenuOpen) return;

    const target = event.target as HTMLElement;

    const toggleButton = document.querySelector('.mobile-menu-toggle');
    const clickedToggle = toggleButton && toggleButton.contains(target);

    const mobileMenu = this.mobileMenuRef?.nativeElement;
    const clickedMenu = mobileMenu && mobileMenu.contains(target);

    if (!clickedToggle && !clickedMenu) {
      this.isMobileMenuOpen = false;
      this.cdr.detectChanges();
    }
  }

  toggleMobileMenu(event: Event) {
    event.stopPropagation();
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
    this.cdr.detectChanges();
  }

  login() {
    this.router.navigate(['/login']);
    this.closeMobileMenuIfOpen();
  }

  signup() {
    this.router.navigate(['/signup']);
    this.closeMobileMenuIfOpen();
  }

  logout() {
    this.authService.logout();
    this.isAuthenticated = false;
    this.closeMobileMenuIfOpen();
  }

  closeMobileMenuIfOpen() {
    if (this.isMobileMenuOpen) {
      this.isMobileMenuOpen = false;
      this.cdr.detectChanges();
    }
  }
}
