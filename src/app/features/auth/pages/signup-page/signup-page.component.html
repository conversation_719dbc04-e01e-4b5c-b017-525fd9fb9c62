<div class="auth-container">
  <app-card class="auth-card">
    <div class="auth-header">
      <h1 class="auth-title">Sign Up</h1>
      <p class="auth-subtitle">Create a new account to get started</p>
    </div>

    <form [formGroup]="signupForm" (ngSubmit)="onSubmit()" class="auth-form">
      <app-alert *ngIf="errorMessage" type="danger" [message]="errorMessage"></app-alert>

      <div class="form-group">
        <app-input
          label="Email"
          type="email"
          placeholder="<EMAIL>"
          formControlName="email"
          [hasError]="!!(signupForm.get('email')?.touched && signupForm.get('email')?.invalid)"
          [errorMessage]="signupForm.get('email')?.touched && signupForm.get('email')?.invalid ?
                  (signupForm.get('email')?.errors?.['required'] ? 'Email is required' :
                  (signupForm.get('email')?.errors?.['email'] ? 'Please enter a valid email' : '')) : ''">
        </app-input>
      </div>

      <div class="form-group">
        <app-input
          label="Password"
          type="password"
          placeholder="At least 6 characters"
          formControlName="password"
          [hasError]="!!(signupForm.get('password')?.touched && signupForm.get('password')?.invalid)"
          [errorMessage]="signupForm.get('password')?.touched && signupForm.get('password')?.invalid ?
                  (signupForm.get('password')?.errors?.['required'] ? 'Password is required' :
                  (signupForm.get('password')?.errors?.['minlength'] ? 'Password should be at least 6 characters' : '')) : ''">
        </app-input>
      </div>

      <div class="form-group">
        <app-input
          label="Confirm Password"
          type="password"
          placeholder="Re-enter your password"
          formControlName="confirmPassword"
          [hasError]="!!(signupForm.get('confirmPassword')?.touched && signupForm.get('confirmPassword')?.invalid)"
          [errorMessage]="signupForm.get('confirmPassword')?.touched && signupForm.get('confirmPassword')?.invalid ?
                  (signupForm.get('confirmPassword')?.errors?.['required'] ? 'Please confirm your password' :
                  (signupForm.get('confirmPassword')?.errors?.['passwordMismatch'] ? 'Passwords do not match' : '')) : ''">
        </app-input>
      </div>

      <div class="form-actions">
        <app-button
          type="primary"
          buttonType="submit"
          [fullWidth]="true"
          [loading]="isLoading"
          [disabled]="isLoading">
          Sign Up
        </app-button>
      </div>
    </form>

    <div class="auth-footer">
      <p>Already have an account? <a routerLink="/login">Login</a></p>
    </div>
  </app-card>
</div>
