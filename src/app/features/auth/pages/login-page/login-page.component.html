<div class="auth-container">
  <app-card class="auth-card">
    <div class="auth-header">
      <h1 class="auth-title">Login</h1>
      <p class="auth-subtitle">Welcome back! Please login to your account.</p>
    </div>

    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="auth-form">
      <app-alert *ngIf="errorMessage" type="danger" [message]="errorMessage"></app-alert>

      <div class="form-group">
        <app-input
          label="Email"
          type="email"
          placeholder="<EMAIL>"
          formControlName="email"
          [hasError]="!!(loginForm.get('email')?.touched && loginForm.get('email')?.invalid)"
          [errorMessage]="loginForm.get('email')?.touched && loginForm.get('email')?.invalid ?
                  (loginForm.get('email')?.errors?.['required'] ? 'Email is required' :
                  (loginForm.get('email')?.errors?.['email'] ? 'Please enter a valid email' : '')) : ''">
        </app-input>
      </div>

      <div class="form-group">
        <app-input
          label="Password"
          type="password"
          placeholder="Enter your password"
          formControlName="password"
          [hasError]="!!(loginForm.get('password')?.touched && loginForm.get('password')?.invalid)"
          [errorMessage]="loginForm.get('password')?.touched && loginForm.get('password')?.invalid ?
                  (loginForm.get('password')?.errors?.['required'] ? 'Password is required' :
                  (loginForm.get('password')?.errors?.['minlength'] ? 'Password should be at least 6 characters' : '')) : ''">
        </app-input>
      </div>

      <div class="form-actions">
        <app-button
          type="primary"
          buttonType="submit"
          [fullWidth]="true"
          [loading]="isLoading"
          [disabled]="isLoading">
          Login
        </app-button>
      </div>
    </form>

    <div class="auth-footer">
      <p>Don't have an account? <a routerLink="/signup">Sign up</a></p>
    </div>
  </app-card>
</div>

