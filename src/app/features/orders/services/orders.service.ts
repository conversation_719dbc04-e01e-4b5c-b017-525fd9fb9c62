import {inject, Injectable, OnDestroy} from '@angular/core';
import {BehaviorSubject, catchError, EMPTY, map, Observable, of, Subject, take, tap} from 'rxjs';
import {HttpClient, HttpParams} from '@angular/common/http';
import {InfiniteDataService, PaginatedResponse, PaginationParams} from '../../../shared/services/infinite-data.service';
import {Order} from '../models/order.model';
import {environment} from '../../../../environments/environment';
import {StompService} from '../../../shared/services/stomp.service';

// Define the ApiResponse interface to match the Spring backend response format
interface ApiResponse {
  content: Order[];
  pageable: {
    pageNumber: number;
    pageSize: number;
    offset: number;
    paged: boolean;
    unpaged: boolean;
  };
  totalElements: number;
  totalPages: number;
  last: boolean;
  first: boolean;
  sort: {
    sorted: boolean;
    unsorted: boolean;
    empty: boolean;
  };
  numberOfElements: number;
  size: number;
  number: number;
  empty: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class OrdersService extends InfiniteDataService<Order> implements OnDestroy {
  private apiUrl = `${environment.apiBaseUrl}`;

  // STOMP destinations
  private ordersSubscription = '/user/queue/orders';         // For getting order updates from subscription
  private ordersMapping = '/app/users/me/orders';            // Direct request to @SubscribeMapping endpoint
  private orderCancelMapping = '/app/orders/{orderId}/cancel'; // @MessageMapping for cancel

  private wsConnected = false;
  private ordersMap = new Map<string, Order>();
  private initialFetch = true;
  private totalElements = new BehaviorSubject<number>(0);
  private stompService = inject(StompService);
  private destroy$ = new Subject<void>();
  private subscriptions: { [key: string]: any } = {};

  constructor(private http: HttpClient) {
    super();
    this.setupStompListeners();
  }

  private setupStompListeners(): void {
    // Track connection status
    this.stompService.isConnected().subscribe(connected => {
      console.log(`STOMP connected: ${connected}`);
      this.wsConnected = connected;

      if (connected) {
        // Subscribe to the orders topic when connected
        this.subscribeToOrdersTopics();
      }
    });

    // The service automatically connects when instantiated
  }

  private subscribeToOrdersTopics(): void {
    if (this.wsConnected) {
      // Subscribe to order updates
      this.subscriptions['updates'] = this.stompService.subscribe(this.ordersSubscription)
        .subscribe((message: any) => {
          console.log('Received order update:', message);

          // Handle different message types
          if (message.type === 'order_update') {
            this.updateOrderInList(message.data);
          } else if (message.type === 'order_new') {
            this.addOrderToList(message.data);
          } else if (message.type === 'order_cancel') {
            this.removeOrderFromList(message.data.id);
          }
        });

      console.log(`Subscribed to ${this.ordersSubscription}`);
    }
  }

  override fetchData(params: PaginationParams): Observable<PaginatedResponse<Order>> {
    this.loading.set(true);

    // If this is the initial fetch or WebSocket isn't connected, try to use HTTP fallback
    if (this.initialFetch || !this.wsConnected) {
      return this.fetchViaHttp(params).pipe(
        tap(() => {
          this.initialFetch = false;
        }),
        catchError(error => {
          console.error('Error fetching data via HTTP:', error);
          this.loading.set(false);
          // Return empty data if both WebSocket and HTTP fail
          return of({
            items: [],
            total: 0,
            page: params.page,
            limit: params.limit,
            hasMore: false
          });
        })
      );
    }

    return this.getFilteredData(params);
  }

  private fetchInitialData(params: PaginationParams): Observable<PaginatedResponse<Order>> {
    // We don't use this method anymore, all initial fetches go through fetchViaHttp now
    // This is kept for backward compatibility
    return this.fetchViaHttp(params);
  }

  private fetchViaHttp(params: PaginationParams): Observable<PaginatedResponse<Order>> {
    console.log('Fetching orders via HTTP fallback');

    // Convert PaginationParams to HttpParams
    let httpParams = new HttpParams()
      .set('page', params.page.toString())
      .set('size', params.limit.toString());

    if (params.sort) {
      httpParams = httpParams.set('sort', `${params.sort.field},${params.sort.direction}`);
    }

    if (params.filter) {
      // Add filter parameters to HttpParams
      Object.entries(params.filter).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          httpParams = httpParams.set(key, value.toString());
        }
      });
    }

    return this.http.get<ApiResponse>(`${this.apiUrl}/orders`, { params: httpParams }).pipe(
      map(response => {
        // Map the API response to PaginatedResponse
        response.content.forEach((order: Order) => {
          this.ordersMap.set(order.id, order);
        });

        this.totalElements.next(response.totalElements);

        const paginatedResponse: PaginatedResponse<Order> = {
          items: response.content,
          total: response.totalElements,
          page: response.pageable.pageNumber,
          limit: response.pageable.pageSize,
          hasMore: !response.last
        };

        return paginatedResponse;
      }),
      catchError(error => {
        console.error('HTTP fallback error:', error);
        return of({
          items: [],
          total: 0,
          page: params.page,
          limit: params.limit,
          hasMore: false
        });
      })
    );
  }

  private getFilteredData(params: PaginationParams): Observable<PaginatedResponse<Order>> {
    const start = params.page * params.limit;
    const end = start + params.limit;

    const allOrders = Array.from(this.ordersMap.values());
    let filteredOrders = this.applyFilters(allOrders, params.filter || {});

    if (params.sort) {
      filteredOrders = this.applySorting(filteredOrders, params.sort);
    }

    const paginatedItems = filteredOrders.slice(start, end);

    const result: PaginatedResponse<Order> = {
      items: paginatedItems,
      total: filteredOrders.length,
      page: params.page,
      limit: params.limit,
      hasMore: end < filteredOrders.length
    };

    this.loading.set(false);
    return of(result);
  }

  private applyFilters(orders: Order[], filters: Record<string, any>): Order[] {
    return orders.filter(order => {
      let matches = true;

      if (filters['status'] && filters['status'] !== 'all') {
        matches = matches && order.status === filters['status'];
      }

      if (filters['pair'] && filters['pair'] !== 'all') {
        matches = matches && order.pair === filters['pair'];
      }

      if (filters['startDate']) {
        const startDate = new Date(filters['startDate']);
        matches = matches && new Date(order.createdAt) >= startDate;
      }

      if (filters['endDate']) {
        const endDate = new Date(filters['endDate']);
        matches = matches && new Date(order.createdAt) <= endDate;
      }

      return matches;
    });
  }

  private applySorting(orders: Order[], sort: { field: string, direction: 'asc' | 'desc' }): Order[] {
    return [...orders].sort((a, b) => {
      const fieldA = this.getNestedProperty(a, sort.field);
      const fieldB = this.getNestedProperty(b, sort.field);

      if (fieldA < fieldB) return sort.direction === 'asc' ? -1 : 1;
      if (fieldA > fieldB) return sort.direction === 'asc' ? 1 : -1;
      return 0;
    });
  }

  private getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((o, key) => o?.[key], obj);
  }

  private updateOrderInList(updatedOrder: Order): void {
    if (updatedOrder && updatedOrder.id) {
      this.ordersMap.set(updatedOrder.id, updatedOrder);
      this.refreshData();
    }
  }

  private addOrderToList(newOrder: Order): void {
    if (newOrder && newOrder.id) {
      this.ordersMap.set(newOrder.id, newOrder);
      this.totalElements.next(this.totalElements.getValue() + 1);
      this.refreshData();
    }
  }

  private removeOrderFromList(orderId: string): void {
    if (this.ordersMap.has(orderId)) {
      this.ordersMap.delete(orderId);
      this.totalElements.next(this.totalElements.getValue() - 1);
      this.refreshData();
    }
  }

  private refreshData(): void {
    this.resetPagination();
    this.loadMore().subscribe();
  }

  cancelOrder(orderId: string): Observable<boolean> {
    const cancelSubject = new Subject<boolean>();

    if (!this.wsConnected) {
      return this.cancelViaHttp(orderId);
    }

    const destination = this.orderCancelMapping.replace('{orderId}', orderId);

    // Send cancel message via STOMP
    this.stompService.send(destination);
    console.log(`Sent cancel request to ${destination}`);

    // The server will send a message to /user/queue/orders which we're already subscribed to
    this.removeOrderFromList(orderId);

    cancelSubject.next(true);
    cancelSubject.complete();

    return cancelSubject.asObservable();
  }

  private cancelViaHttp(orderId: string): Observable<boolean> {
    return this.http.post<boolean>(`${this.apiUrl}/orders/${orderId}/cancel`, {}).pipe(
      tap(() => {
        this.removeOrderFromList(orderId);
      })
    );
  }

  isWebSocketConnected(): Observable<boolean> {
    return this.stompService.isConnected();
  }

  override setFilter(filter: Record<string, any>) {
    super.setFilter(filter);
    // No need to manually send filter - it will be included in next request
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    Object.values(this.subscriptions).forEach(subscription => {
      if (subscription && typeof subscription.unsubscribe === 'function') {
        subscription.unsubscribe();
      }
    });

    this.destroy$.next();
    this.destroy$.complete();
  }
}
