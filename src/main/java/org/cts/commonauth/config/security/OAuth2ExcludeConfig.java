package org.cts.commonauth.config.security;

import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.security.oauth2.resource.servlet.OAuth2ResourceServerAutoConfiguration;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration class to exclude the automatic OAuth2 Resource Server auto-configuration.
 * This allows us to have complete control over the JWT configuration and authentication flow.
 */
@Configuration
@EnableAutoConfiguration(exclude = {
        OAuth2ResourceServerAutoConfiguration.class
})
public class OAuth2ExcludeConfig {
}
