package org.cts.commonauth.config.security;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.oauth2.core.DelegatingOAuth2TokenValidator;
import org.springframework.security.oauth2.core.OAuth2TokenValidator;
import org.springframework.security.oauth2.jwt.*;
import org.springframework.core.io.Resource;

import java.io.InputStream;
import java.security.KeyFactory;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

import static org.cts.commonauth.config.Constants.KEY_ALGORITHM_RSA;

@Configuration
public class CommonAuthJwtConfig {

    private final RSAPublicKey publicKey;

    @Value("${jwt.issuer:https://example.com}")
    private String issuer;

    public CommonAuthJwtConfig(@Value("${jwt.public-key-path}") Resource publicKeyPath) {
        this.publicKey = getPublicKeyFromResource(publicKeyPath);
    }

    @Bean
    public RSAPublicKey jwtPublicKey() {
        return this.publicKey;
    }

    @Bean
    public JwtDecoder jwtDecoder() {
        NimbusJwtDecoder jwtDecoder = NimbusJwtDecoder.withPublicKey(publicKey).build();

        OAuth2TokenValidator<Jwt> audienceValidator = new JwtIssuerValidator(issuer);
        OAuth2TokenValidator<Jwt> withIssuer = JwtValidators.createDefaultWithIssuer(issuer);
        OAuth2TokenValidator<Jwt> withAudience = new DelegatingOAuth2TokenValidator<>(withIssuer, audienceValidator);

        jwtDecoder.setJwtValidator(withAudience);

        return jwtDecoder;
    }

    public static RSAPublicKey getPublicKeyFromResource(Resource resource) {
        try {
            KeyFactory kf = KeyFactory.getInstance(KEY_ALGORITHM_RSA);

            String publicPem = getFormattedKeyFromResource(resource);
            byte[] publicBytes = Base64.getDecoder().decode(publicPem);
            X509EncodedKeySpec publicSpec = new X509EncodedKeySpec(publicBytes);

            return (RSAPublicKey) kf.generatePublic(publicSpec);
        } catch (Exception e) {
            throw new RuntimeException("Error reading public key: " + e.getMessage());
        }
    }

    public static RSAPrivateKey getPrivateKeyFromResource(Resource resource) {
        try {
            KeyFactory kf = KeyFactory.getInstance(KEY_ALGORITHM_RSA);

            String privatePem = getFormattedKeyFromResource(resource);
            byte[] privateBytes = Base64.getDecoder().decode(privatePem);
            X509EncodedKeySpec publicSpec = new X509EncodedKeySpec(privateBytes);

            return (RSAPrivateKey) kf.generatePrivate(publicSpec);
        } catch (Exception e) {
            throw new RuntimeException("Error reading private key: " + e.getMessage());
        }
    }

    private static String getFormattedKeyFromResource(Resource resource) throws Exception {
        try (InputStream is = resource.getInputStream()) {
            return new String(is.readAllBytes())
                    .replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replace("-----BEGIN PUBLIC KEY-----", "")
                    .replace("-----END PUBLIC KEY-----", "")
                    .replaceAll("\\s", "");
        }
    }
}
