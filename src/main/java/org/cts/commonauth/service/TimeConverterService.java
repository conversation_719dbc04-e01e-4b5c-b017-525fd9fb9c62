package org.cts.commonauth.service;

import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneOffset;

/**
 * This class is used to convert between LocalDateTime and Timestamp.
 * This utility class is especially useful when working with gRPC services that use com.google.protobuf.Timestamp.
 */
@Component
public class TimeConverterService {

    /**
     * Converts LocalDateTime to epoch milliseconds
     * @param localDateTime the LocalDateTime to convert
     * @return epoch milliseconds
     */
    public long convertToEpochMilli(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return 0;
        }
        return localDateTime.toInstant(ZoneOffset.UTC).toEpochMilli();
    }

    /**
     * Converts epoch milliseconds to LocalDateTime
     * @param epochMilli the epoch milliseconds to convert
     * @return LocalDateTime
     */
    public LocalDateTime convertFromEpochMilli(long epochMilli) {
        return LocalDateTime.ofEpochSecond(epochMilli / 1000, (int) (epochMilli % 1000) * 1000000, ZoneOffset.UTC);
    }
}