package org.cts.commonauth.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.cts.commonauth.AllUserDetails;
import org.cts.commonauth.exceptions.InvalidRequestException;
import org.cts.commonauth.model.enumType.TokenType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseCookie;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.*;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.util.Objects;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class JwtService {
    private final JwtDecoder decoder;

    @Value("${jwt.access-token.expiry:PT1H}")
    private Duration accessTokenExpiry;

    @Value("${jwt.refresh-token.expiry:P7D}")
    private Duration refreshTokenExpiry;

    @Value("${jwt.issuer:https://example.com}")
    private String issuer;

    public boolean isNotValidToken(String token) {
        try {
            log.debug("Validating token");
            Jwt jwt = decoder.decode(token);
            boolean isExpired = Objects.requireNonNull(jwt.getExpiresAt()).isBefore(Instant.now());
            if (isExpired) {
                log.debug("Token is expired. Expiry: {}, Current time: {}", jwt.getExpiresAt(), Instant.now());
                return true;
            }
            log.debug("Token is valid");
            return false;
        } catch (Exception e) {
            log.warn("Error validating token: {}", e.getMessage());
            return false;
        }
    }

    public String getUserIdFromToken(String token) throws JwtException {
        try {
            log.debug("Extracting user ID from token");
            Jwt jwt = decoder.decode(token);
            String userId = jwt.getSubject();

            if (userId == null || userId.isEmpty()) {
                log.warn("User ID is null or empty in token");
                throw new JwtException("userId is null or empty");
            }

            log.debug("User ID extracted: {}", userId);
            return userId;
        } catch (JwtException e) {
            log.warn("JWT exception while extracting user ID: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected exception while extracting user ID: {}", e.getMessage());
            throw new JwtException("exception while parsing token: " + e.getMessage(), e);
        }
    }

    public ResponseCookie tokenToCookie(String token, TokenType type) {
        log.debug("Converting {} to cookie", type);
        try {
            ResponseCookie cookie;
            if (type == TokenType.ACCESS_TOKEN) {
                cookie = ResponseCookie.from(type.toString(), token)
                        .httpOnly(true)
                        .path("/")
                        .maxAge(accessTokenExpiry)
                        .sameSite("Strict")
                        .build();
            } else if (type == TokenType.REFRESH_TOKEN) {
                cookie = ResponseCookie.from(type.toString(), token)
                        .httpOnly(true)
                        .path("/")
                        .maxAge(refreshTokenExpiry)
                        .sameSite("Strict")
                        .build();
            } else {
                log.warn("Unknown token type: {}", type);
                throw new InvalidRequestException("Unknown token type");
            }
            log.debug("Cookie created successfully for type: {}", type);
            return cookie;
        } catch (Exception e) {
            log.error("Error creating cookie for token type {}: {}", type, e.getMessage());
            throw e;
        }
    }

    public AllUserDetails parseToken(String token) {
        log.debug("Parsing token to extract user details");
        try {
            Jwt jwt = decoder.decode(token);
            String userId = jwt.getSubject();

            if (userId == null || userId.isEmpty()) {
                log.warn("User ID is null or empty in token");
                throw new JwtException("userId is null or empty");
            }

            String username = jwt.getClaimAsString("username");
            var authorities = jwt.getClaimAsStringList("roles").stream()
                    .map(SimpleGrantedAuthority::new).toList();

            log.debug("Token parsed successfully. User ID: {}, Username: {}, Authorities: {}",
                    userId, username, authorities);

            return AllUserDetails.builder()
                    .id(UUID.fromString(userId))
                    .username(username)
                    .authorities(authorities)
                    .build();
        } catch (JwtException e) {
            log.warn("JWT exception while parsing token: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected exception while parsing token: {}", e.getMessage(), e);
            throw new JwtException("exception while parsing token: " + e.getMessage(), e);
        }
    }

    public JwtClaimsSet buildAccessClaimsSet(AllUserDetails user, Instant issuedAt) {
        log.debug("Building access token claims for user: {}", user.getUsername());
        return JwtClaimsSet.builder()
                .issuer(issuer)
                .issuedAt(issuedAt)
                .expiresAt(issuedAt.plus(accessTokenExpiry))
                .subject(String.valueOf(user.getId()))
                .claim("username", user.getUsername())
                .claim("type", TokenType.ACCESS_TOKEN.name())
                .claim("roles", user.getAuthorities())
                .build();
    }

    public JwtClaimsSet buildRefreshClaimsSet(AllUserDetails user, Instant issuedAt) {
        log.debug("Building refresh token claims for user: {}", user.getUsername());
        return JwtClaimsSet.builder()
                .issuer(issuer)
                .issuedAt(issuedAt)
                .expiresAt(issuedAt.plus(refreshTokenExpiry))
                .subject(String.valueOf(user.getId()))
                .claim("username", user.getUsername())
                .claim("type", TokenType.REFRESH_TOKEN.name())
                .build();
    }
}
