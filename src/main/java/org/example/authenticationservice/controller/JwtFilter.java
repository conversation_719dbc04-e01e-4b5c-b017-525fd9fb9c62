package org.cts.commonauth.config.security;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.cts.commonauth.AllUserDetails;
import org.cts.commonauth.exceptions.InvalidTokenException;
import org.cts.commonauth.model.enumType.TokenType;
import org.cts.commonauth.service.JwtService;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collection;

/**
 * This class is used to filter the incoming requests and validate the JWT token.
 * Create a bean of this class in the security configuration class to enable this filter.
 */
@Slf4j
@RequiredArgsConstructor
public class JwtFilter extends OncePerRequestFilter {

    private final JwtService jwtService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        log.debug("Processing request: {} {}", request.getMethod(), request.getRequestURI());
        Cookie[] cookies = request.getCookies();
        if (cookies == null || cookies.length == 0) {
            log.debug("No cookies found in request, skipping authentication");
            filterChain.doFilter(request, response);
            return;
        }

        String token = Arrays.stream(cookies).filter(cookie -> cookie.getName().equals(TokenType.ACCESS_TOKEN.name()))
                .findFirst().map(Cookie::getValue).orElse(null);

        if (token == null) {
            log.debug("No access token found in cookies, skipping authentication");
            filterChain.doFilter(request, response);
            return;
        }

        log.debug("Access token found in request, attempting to parse");
        try {
            AllUserDetails user = jwtService.parseToken(token);

            if (jwtService.isNotValidToken(token)) {
                log.warn("Invalid token detected: expired or malformed");
                throw new InvalidTokenException("Token is not valid");
            }

            log.info("Successful authentication for user: {}", user.getUsername());
            setupSecurityContext(request, user);
            filterChain.doFilter(request, response);
        } catch (InvalidTokenException e) {
            log.warn("Invalid token exception: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during token processing", e);
            throw e;
        }
    }

    private static void setupSecurityContext(HttpServletRequest request, AllUserDetails user) {
        Collection<? extends GrantedAuthority> authorities = user.getAuthorities();
        UsernamePasswordAuthenticationToken authentication =
                new UsernamePasswordAuthenticationToken(user, null, authorities);

        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
        SecurityContextHolder.getContext().setAuthentication(authentication);
        log.debug("Security context set with authorities: {}", authorities);
    }
}
