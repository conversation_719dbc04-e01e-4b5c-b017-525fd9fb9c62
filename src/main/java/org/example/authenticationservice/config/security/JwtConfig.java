package org.example.authenticationservice.config.security;

import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jose.jwk.source.ImmutableJWKSet;
import com.nimbusds.jose.jwk.source.JWKSource;
import com.nimbusds.jose.proc.SecurityContext;
import java.security.interfaces.RSAPrivateKey;

import org.cts.commonauth.config.security.CommonAuthJwtConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.security.oauth2.jwt.JwtEncoder;
import org.springframework.security.oauth2.jwt.NimbusJwtEncoder;

import java.security.interfaces.RSAPublicKey;
import java.util.UUID;

@Configuration
public class JwtConfig {
    private final RSAPrivateKey privateKey;
    private final RSAPublicKey publicKey;

    public JwtConfig(@Value("${jwt.private-key-path}") Resource privateKeyPath, RSAPublicKey publicKey) {
        this.privateKey = CommonAuthJwtConfig.getPrivateKeyFromResource(privateKeyPath);
        this.publicKey = publicKey;
    }


    @Bean
    public JwtEncoder jwtEncoder() {
        JWKSource<SecurityContext> jwkSource;

        RSAKey rsaKey = new RSAKey.Builder(publicKey).privateKey(privateKey)
                .keyID(UUID.randomUUID().toString()).build();
        JWKSet jwkSet = new JWKSet(rsaKey);
        jwkSource = new ImmutableJWKSet<>(jwkSet);

        return new NimbusJwtEncoder(jwkSource);
    }
}
