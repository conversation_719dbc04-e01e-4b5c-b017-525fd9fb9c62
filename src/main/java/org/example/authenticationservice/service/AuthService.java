package org.example.authenticationservice.service;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.cts.commonauth.AllUserDetails;
import org.cts.commonauth.exceptions.InvalidRequestException;
import org.cts.commonauth.exceptions.InvalidTokenException;
import org.cts.commonauth.exceptions.UserNotFoundException;
import org.cts.commonauth.model.enumType.TokenType;
import org.cts.commonauth.service.JwtService;
import org.springframework.http.ResponseCookie;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.jwt.JwtClaimsSet;
import org.springframework.security.oauth2.jwt.JwtEncoder;
import org.springframework.security.oauth2.jwt.JwtEncoderParameters;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Arrays;
import java.util.Map;

import static org.cts.commonauth.config.Constants.EMAIL_REGEX;
import static org.cts.commonauth.config.Constants.PASSWORD_REGEX;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {
    private final JwtService jwtService;
    private final UserService userService;
    private final PasswordEncoder passwordEncoder;

    private final JwtEncoder encoder;



    public Map<TokenType, ResponseCookie> login(@Pattern(regexp = EMAIL_REGEX) String username, @Pattern(regexp = PASSWORD_REGEX) String password) {
        AllUserDetails userDetails = userService.loadUserByUsername(username);

        log.info("password: {}/encoded: {}", password, passwordEncoder.encode(password));
        log.info("userDetails.getPassword(): {}", userDetails.getPassword());

        if (!userDetails.getPassword().equals(password)) {
            throw new InvalidRequestException("Invalid credentials");
        }

        String accessToken = generateAccessToken(userDetails);
        String refreshToken = generateRefreshToken(userDetails);

        return Map.of(
                TokenType.ACCESS_TOKEN, jwtService.tokenToCookie(accessToken, TokenType.ACCESS_TOKEN),
                TokenType.REFRESH_TOKEN, jwtService.tokenToCookie(refreshToken, TokenType.REFRESH_TOKEN)
        );
    }

    public Map<TokenType, ResponseCookie> getEmptyTokens() {
        ResponseCookie accessCookie = jwtService.tokenToCookie("", TokenType.ACCESS_TOKEN);
        ResponseCookie refreshCookie = jwtService.tokenToCookie("", TokenType.REFRESH_TOKEN);

        return Map.of(
                TokenType.ACCESS_TOKEN, accessCookie,
                TokenType.REFRESH_TOKEN, refreshCookie
        );
    }


    public String register(@Pattern(regexp = EMAIL_REGEX) String username, @Pattern(regexp = PASSWORD_REGEX) String password) {
        return userService.register(username, password);
    }

    public ResponseCookie refresh(HttpServletRequest request){
        Cookie[] cookies = request.getCookies();

        final String refreshToken = Arrays.stream(cookies)
                .filter(cookie -> cookie.getName().equals(TokenType.REFRESH_TOKEN.name()))
                .findFirst().map(Cookie::getValue).orElse(null);

        if (jwtService.isNotValidToken(refreshToken)) {
            throw new InvalidRequestException("Invalid refresh token");
        }

        String token = generateAccessToken(refreshToken);

        return jwtService.tokenToCookie(token, TokenType.ACCESS_TOKEN);
    }

    public String generateAccessToken(@Valid AllUserDetails user) {
        Instant now = Instant.now();

        JwtClaimsSet claims = jwtService.buildAccessClaimsSet(user, Instant.now());

        return this.encoder.encode(JwtEncoderParameters.from(claims)).getTokenValue();
    }

    public String generateRefreshToken(@Valid AllUserDetails user) {
        Instant now = Instant.now();

        JwtClaimsSet claims = jwtService.buildRefreshClaimsSet(user, Instant.now());

        return this.encoder.encode(JwtEncoderParameters.from(claims)).getTokenValue();
    }

    public String generateAccessToken(String refreshToken) {
        if (jwtService.isNotValidToken(refreshToken))
            throw new InvalidTokenException("Refresh token is not valid");

        String userId = jwtService.getUserIdFromToken(refreshToken);
        AllUserDetails user = userService.getAllUserDetailsById(userId)
                .orElseThrow(() -> new UserNotFoundException("User not found"));

        return generateAccessToken(user);
    }
}
