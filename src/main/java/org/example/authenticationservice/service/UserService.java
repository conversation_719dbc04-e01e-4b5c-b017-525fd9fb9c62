package org.example.authenticationservice.service;

import jakarta.validation.constraints.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.cts.commonauth.AllUserDetails;
import org.cts.commonauth.exceptions.UserAlreadyExistsException;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.UUID;

import static org.cts.commonauth.config.Constants.EMAIL_REGEX;
import static org.cts.commonauth.config.Constants.PASSWORD_REGEX;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserService implements UserDetailsService {

    private final PasswordEncoder passwordEncoder;
    private final UserServiceGrpcClient userServiceGrpcClient;

    @Override
    public AllUserDetails loadUserByUsername(@Pattern(regexp = EMAIL_REGEX) String username) throws UsernameNotFoundException {
        log.info("Loading user by username: {}", username);

        return userServiceGrpcClient.findByUsername(username).orElseThrow(() -> new UsernameNotFoundException("User not found: " + username));
    }

    public Optional<AllUserDetails> getAllUserDetailsById(String userId) {
        log.info("Getting user details by ID: {}", userId);

        try {
            UUID uuid = UUID.fromString(userId);
            return userServiceGrpcClient.findById(uuid);
        } catch (Exception e) {
            log.error("Error getting user details by ID: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    public String register(@Pattern(regexp = EMAIL_REGEX) String username, @Pattern(regexp = PASSWORD_REGEX) String password) {
        log.info("Registering user: {}", username);

        if (userServiceGrpcClient.existsByUsername(username)) {
            throw new UserAlreadyExistsException("Username already exists: " + username);
        }

        String encodedPassword = passwordEncoder.encode(password);
        String userId = userServiceGrpcClient.registerUser(username, encodedPassword);

        log.info("User registered successfully with ID: {}", userId);
        return userId;
    }
}
