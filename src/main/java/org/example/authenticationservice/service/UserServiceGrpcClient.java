package org.example.authenticationservice.service;

import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.cts.commonauth.AllUserDetails;
import org.example.user.grpc.*;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceGrpcClient {

    private final UserServiceGrpc.UserServiceBlockingStub userServiceStub;
    private final ModelMapper modelMapper;

    public Optional<AllUserDetails> findByUsername(String username) {
        try {
            LoadUserByUsernameRequest request = LoadUserByUsernameRequest.newBuilder()
                    .setUsername(username)
                    .build();

            LoadUserByUsernameResponse response = userServiceStub.loadUserByUsername(request);

            return Optional.of(modelMapper.map(response.getUserDetails(), AllUserDetails.class));
        } catch (StatusRuntimeException e) {
            if (e.getStatus().getCode() == Status.Code.NOT_FOUND) {
                log.warn("User not found: {}", username);
                return Optional.empty();
            }
            log.error("Error calling user service: {}", e.getMessage(), e);
            throw e;
        }
    }

    public Optional<AllUserDetails> findById(UUID id) {
        try {
            GetUserDetailsByIdRequest request = GetUserDetailsByIdRequest.newBuilder()
                    .setUserId(id.toString())
                    .build();

            GetUserDetailsByIdResponse response = userServiceStub.getUserDetailsById(request);

            return Optional.of(modelMapper.map(response.getUserDetails(), AllUserDetails.class));
        } catch (StatusRuntimeException e) {
            if (e.getStatus().getCode() == Status.Code.NOT_FOUND) {
                log.warn("User not found with ID: {}", id);
                return Optional.empty();
            }
            log.error("Error calling user service: {}", e.getMessage(), e);
            throw e;
        }
    }

    public String registerUser(String username, String password) {
        try {
            RegisterUserRequest request = RegisterUserRequest.newBuilder()
                    .setUsername(username)
                    .setPassword(password)
                    .build();

            RegisterUserResponse response = userServiceStub.registerUser(request);

            log.info("User registered successfully: {}", response.getUserId());
            return response.getUserId();
        } catch (StatusRuntimeException e) {
            log.error("Error registering user: {}", e.getMessage(), e);
            throw new IllegalArgumentException("Failed to register user: " + e.getMessage());
        }
    }

    public boolean existsByUsername(String username) {
        try {
            return findByUsername(username).isPresent();
        } catch (StatusRuntimeException e) {
            if (e.getStatus().getCode() == Status.Code.NOT_FOUND) {
                return false;
            }
            log.error("Error checking if user exists: {}", e.getMessage(), e);
            throw e;
        }
    }
}
