syntax = "proto3";

package org.example.user;

option java_package = "org.example.user.grpc";
option java_multiple_files = true;
option java_outer_classname = "UserServiceProto";

service UserService {
  rpc LoadUserByUsername(LoadUserByUsernameRequest) returns (LoadUserByUsernameResponse);
  
  rpc GetUserDetailsById(GetUserDetailsByIdRequest) returns (GetUserDetailsByIdResponse);
  
  rpc RegisterUser(RegisterUserRequest) returns (RegisterUserResponse);
}

message LoadUserByUsernameRequest {
  string username = 1;
}

message LoadUserByUsernameResponse {
  UserDetails user_details = 1;
}

message GetUserDetailsByIdRequest {
  string user_id = 1;
}

message GetUserDetailsByIdResponse {
  UserDetails user_details = 1;
}

message RegisterUserRequest {
  string username = 1;
  string password = 2;
}

message RegisterUserResponse {
  bool success = 1;
  string user_id = 2;
  string message = 3;
}

message UserDetails {
  string user_id = 1;
  string username = 2;
  string password = 3;
  repeated string roles = 4;
  bool enabled = 5;
  int64 created_at = 6;
  int64 updated_at = 7;
}
