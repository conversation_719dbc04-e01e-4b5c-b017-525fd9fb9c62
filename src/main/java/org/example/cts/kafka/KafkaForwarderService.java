package org.example.cts.kafka;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.cts.dto.CryptoPriceEvent;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class KafkaForwarderService {

    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper;

    @Value("${kafka.topic.output}")
    private String outputTopic;

    @KafkaListener(topics = "${kafka.topic.input}", groupId = "${spring.kafka.consumer.group-id}")
    public void listenAndForward(String rawMessage) {
        try {
            CryptoPriceEvent event = objectMapper.readValue(rawMessage, CryptoPriceEvent.class);
            log.info("Received from input topic: {}", event);

            kafkaTemplate.send(outputTopic, rawMessage);
            log.info("Forwarded to output topic: {}", outputTopic);
        } catch (Exception e) {
            log.error("Failed to forward message: {}", rawMessage, e);
        }
    }
}
