package org.example.cts.currencyservice.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.cts.currencyservice.config.CloudProperties;
import org.example.cts.currencyservice.dto.CryptoPriceEvent;
import org.example.cts.currencyservice.model.ExchangeRate;
import org.example.cts.currencyservice.model.enumType.GranularityPairs;
import org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@Service
@Slf4j
@RequiredArgsConstructor
public class CurrencyService {

    private final CloudProperties cloudProperties;
    private final PairsService pairsService;
    private final ObjectMapper objectMapper;
    private final MongoTemplate mongoTemplate;
    private final ConcurrentHashMap<String, CryptoPriceEvent> buffer = new ConcurrentHashMap<>();


    @PostConstruct
    public void init() {
        log.info("Application started with configuration pairs: {}", cloudProperties.getPairs());
        pairsService.filterMissingPairs();
    }

    @EventListener(RefreshScopeRefreshedEvent.class)
    public void onRefresh() {
        log.info("Config refreshed");
        pairsService.filterMissingPairs();
    }

    public void handleNewPrice(String rawMessage) {
        try {
            CryptoPriceEvent event = objectMapper.readValue(rawMessage, CryptoPriceEvent.class);

            if (event.symbol() != null && event.price() != null && event.price().signum() > 0) {
                buffer.put(event.symbol(), event);
            }


        } catch (Exception e) {
            log.error("Error parsing price: {}", rawMessage, e);
        }
    }

    @Scheduled(fixedRate = 5000)
    public void flushBufferToMongo() {
        if (buffer.isEmpty()) {
            return;
        }

        log.info("Flushing {} exchange rates to Mongo", buffer.size());

        List<ExchangeRate> exchangeRates = buffer.values().stream()
                .map(event -> {
                    Instant timestamp = Instant.ofEpochMilli((long) (event.timestamp() * 1000));
                    return ExchangeRate.builder()
                            .price(event.price())
                            .pairs(event.symbol())
                            .timestamp(timestamp)
                            .granularity(GranularityPairs.SECONDS_5)
                            .build();
                })
                .toList();

        try {
            mongoTemplate.insert(exchangeRates, "exchange_rates");
        } catch (Exception e) {
            log.error("Failed to insert exchange rates in bulk", e);
        } finally {
            buffer.clear();
        }
    }
}

