package org.example.cts.currencyservice.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.cts.currencyservice.dto.BasePairsDto;
import org.example.cts.currencyservice.dto.PairsDetailsDto;
import org.example.cts.currencyservice.model.SupportedPair;
import org.example.cts.currencyservice.repository.SupportedPairRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class SupportedPairService {

    private final SupportedPairRepository supportedPairRepository;
    private final MongoTemplate mongoTemplate;

    public Page<BasePairsDto> getPairs(int page, int size, String sort, String direction, String searchSymbol) {
        Sort.Direction sortDirection = Sort.Direction.fromString(direction);
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

        Query query = new Query().with(pageable);

        if (searchSymbol != null && !searchSymbol.isEmpty()) {
            query.addCriteria(Criteria.where("pairSymbol").regex(searchSymbol, "i"));
        }

        List<SupportedPair> pairs = mongoTemplate.find(query, SupportedPair.class);

        if (pairs.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Pairs not found");
        }

        List<BasePairsDto> dtoList = pairs.stream()
                .map(pair -> new BasePairsDto(
                        pair.getId().toString(),
                        pair.getBaseCurrencyCode(),
                        pair.getTargetCurrencyCode(),
                        pair.getPairSymbol()))
                .collect(Collectors.toList());

        long totalCount = searchSymbol != null && !searchSymbol.isEmpty()
                ? mongoTemplate.count(Query.of(query).limit(-1).skip(-1), SupportedPair.class)
                : supportedPairRepository.count();

        return PageableExecutionUtils.getPage(dtoList, pageable, () -> totalCount);
    }

    public PairsDetailsDto getPairById(UUID id) {
        SupportedPair pair = supportedPairRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Pair not found"));

        return PairsDetailsDto.mapToDto(pair);
    }

    public PairsDetailsDto getPairByPairSymbol(String pairSymbol) {
        SupportedPair pair = supportedPairRepository.findByPairSymbol(pairSymbol)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Pair not found"));

        return PairsDetailsDto.mapToDto(pair);
    }
}