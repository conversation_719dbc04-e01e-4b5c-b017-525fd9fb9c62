package org.example.cts.currencyservice.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.cts.currencyservice.config.CloudProperties;
import org.example.cts.currencyservice.dto.FilterInfo;
import org.example.cts.currencyservice.dto.PairInfo;
import org.example.cts.currencyservice.dto.SymbolInfo;
import org.example.cts.currencyservice.model.SupportedPair;
import org.example.cts.currencyservice.repository.SupportedPairRepository;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class PairsService {

    private final CloudProperties cloudProperties;
    private final SupportedPairRepository supportedPairRepository;
    private final ObjectMapper objectMapper;
    private final RestTemplate restTemplate;

    @Value("${binance.pair.info.url}")
    private String binancePairInfoUrl;
    private static final BigDecimal DEFAULT_FEE_PERCENTAGE = new BigDecimal("0.10");
    private static final String MIN_ORDER_FILTER = "NOTIONAL";
    private static final String PRICE_PRECISION_FILTER = "PRICE_FILTER";
    private static final String LOT_SIZE_FILTER = "LOT_SIZE";
    private static final String NODE_NAME = "symbols";


    @Transactional
    public void filterMissingPairs() {
        log.info("Filtering missing pairs from configuration...");

        List<String> configuredPairs = cloudProperties.getPairs();
        if (configuredPairs == null || configuredPairs.isEmpty()) {
            log.warn("No pairs configured in cloud properties");
            return;
        }

        List<SupportedPair> existingPairs = supportedPairRepository.findAllByPairSymbolIn(configuredPairs);
        Set<String> existingSymbols = existingPairs.stream()
                .map(SupportedPair::getPairSymbol)
                .collect(Collectors.toSet());

        List<String> missingPairs = configuredPairs.stream()
                .filter(pair -> !existingSymbols.contains(pair))
                .toList();

        handleMissingPairs(missingPairs);
    }

    private void handleMissingPairs(List<String> missingPairs) {
        if (missingPairs.isEmpty()) {
            log.info("No missing pairs to fetch");
            return;
        }

        log.info("Found {} missing pairs to fetch: {}", missingPairs.size(), missingPairs);
        fetchAndSavePairs(missingPairs);
    }

    private void fetchAndSavePairs(List<String> pairsToFetch) {
        List<SupportedPair> pairsToSave = new ArrayList<>();

        for (String pairSymbol : pairsToFetch) {
            try {
                log.info("Fetching information for pair: {}", pairSymbol);
                String url = binancePairInfoUrl + pairSymbol;

                ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
                JsonNode rootNode = objectMapper.readTree(response.getBody());
                JsonNode symbolsNode = rootNode.get(NODE_NAME);

                if (symbolsNode == null || !symbolsNode.isArray()) {
                    log.warn("No symbol information found for pair: {}", pairSymbol);
                    continue;
                }

                PairInfo pairInfo = objectMapper.readValue(response.getBody(), PairInfo.class);

                SymbolInfo symbol = pairInfo.getSymbols().get(0);

                if (symbol == null) {
                    log.warn("No symbol information found for pair: {}", pairSymbol);
                    continue;
                }

                SupportedPair newPair = SupportedPair.builder()
                        .id(UUID.randomUUID())
                        .baseCurrencyCode(symbol.getBaseAsset())
                        .targetCurrencyCode(symbol.getQuoteAsset())
                        .pairSymbol(symbol.getSymbol())
                        .minOrderAmount(findMinNotional(symbol.getFilters()))
                        .pricePrecision(findPricePrecision(symbol.getFilters()))
                        .quantityPrecision(findQuantityPrecision(symbol.getFilters()))
                        .feePercentage(DEFAULT_FEE_PERCENTAGE)
                        .build();

                pairsToSave.add(newPair);

            } catch (Exception e) {
                log.error("Error fetching information for pair: {}", pairSymbol, e);
            }
        }

        if (!pairsToSave.isEmpty()) {
            supportedPairRepository.saveAll(pairsToSave);
            log.info("Successfully saved {} new pairs", pairsToSave.size());
        }
    }

    private int findQuantityPrecision(List<FilterInfo> filters) {
        if (filters != null && !filters.isEmpty()) {
            for (FilterInfo filter : filters) {
                if (LOT_SIZE_FILTER.equals(filter.getFilterType())) {
                    return filter.getStepSize().indexOf('1');
                }
            }
        }
        return 8;
    }

    private int findPricePrecision(List<FilterInfo> filters) {
        if (filters != null && !filters.isEmpty()) {
            for (FilterInfo filter : filters) {
                if (PRICE_PRECISION_FILTER.equals(filter.getFilterType())) {
                    return filter.getMinPrice().indexOf('1');
                }
            }
        }
        return 8;
    }

    private BigDecimal findMinNotional(List<FilterInfo> filters) {
        if (filters != null && !filters.isEmpty()) {
            for (FilterInfo filter : filters) {
                if (MIN_ORDER_FILTER.equals(filter.getFilterType())) {
                    return new BigDecimal(filter.getMinNotional());
                }
            }
        }
        return BigDecimal.ONE;
    }
}
