package org.example.cts.currencyservice.controller;

import jakarta.validation.constraints.Min;
import lombok.RequiredArgsConstructor;
import org.example.cts.currencyservice.dto.BasePairsDto;
import org.example.cts.currencyservice.dto.PaginatedResponse;
import org.example.cts.currencyservice.service.SupportedPairService;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

import static org.example.cts.currencyservice.config.AppConfig.BASE_URL;

@RestController
@RequestMapping(BASE_URL + "/currencies")
@RequiredArgsConstructor
public class SupportedPairController {

    private final SupportedPairService supportedPairService;

    @GetMapping
    public ResponseEntity<PaginatedResponse<BasePairsDto>> getPairs(
            @RequestParam(value = "page", defaultValue = "0") @Min(0) int page,
            @RequestParam(value = "size", defaultValue = "10") @Min(1) int size,
            @RequestParam(defaultValue = "pairSymbol") String sort,
            @RequestParam(value = "direction", defaultValue = "asc") String direction,
            @RequestParam(value = "search", defaultValue = "") String searchSymbol
    ) {
        Page<BasePairsDto> pageResult = supportedPairService.getPairs(page, size, sort, direction, searchSymbol);
        return ResponseEntity.ok(PaginatedResponse.from(pageResult));
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getPairById(@PathVariable UUID id) {
        return ResponseEntity.ok(supportedPairService.getPairById(id));
    }

    @GetMapping("/symbol/{pairSymbol}")
    public ResponseEntity<?> getPairByPairSymbol(@PathVariable String pairSymbol) {
        return ResponseEntity.ok(supportedPairService.getPairByPairSymbol(pairSymbol));
    }

}
