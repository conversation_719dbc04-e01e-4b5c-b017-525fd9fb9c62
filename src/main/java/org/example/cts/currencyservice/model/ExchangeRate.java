package org.example.cts.currencyservice.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.example.cts.currencyservice.model.enumType.GranularityPairs;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.TimeSeries;
import java.math.BigDecimal;
import java.time.Instant;

@Document(collection = "exchange_rates")
@TimeSeries(timeField = "timestamp", metaField = "pairs")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
public class ExchangeRate {

    private Instant timestamp;

    @Field("pairs")
    private String pairs;

    private BigDecimal price;

    private GranularityPairs granularity;

}
