package org.example.cts.currencyservice.model;

import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;
import java.util.UUID;

@Document(collection = "supported_pairs")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
public class SupportedPair {

    @Id
    private UUID id;

    private String baseCurrencyCode;

    private String targetCurrencyCode;

    private String pairSymbol;

    private BigDecimal minOrderAmount;

    private BigDecimal feePercentage;

    private int pricePrecision;

    private int quantityPrecision;

}
