package org.example.cts.currencyservice.repository;

import org.example.cts.currencyservice.model.SupportedPair;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface SupportedPairRepository extends MongoRepository<SupportedPair, UUID> {
    List<SupportedPair> findAllByPairSymbolIn(List<String> pairsSymbol);
    Optional<SupportedPair> findByPairSymbol(String pairSymbol);
}
