package org.example.cts.currencyservice.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.example.cts.currencyservice.model.SupportedPair;

import java.math.BigDecimal;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class PairsDetailsDto {
    private String baseCode;
    private String targetCode;
    private String pairSymbol;
    private BigDecimal minOrderAmount;
    private BigDecimal feePercentage;
    private int pricePrecision;
    private int quantityPrecision;

    public static PairsDetailsDto mapToDto(SupportedPair pair) {
        return new PairsDetailsDto(
                pair.getBaseCurrencyCode(),
                pair.getTargetCurrencyCode(),
                pair.getPairSymbol(),
                pair.getMinOrderAmount(),
                pair.getFeePercentage(),
                pair.getPricePrecision(),
                pair.getQuantityPrecision());
    }
}
