package org.example.cts.currencyservice.dto;

import lombok.*;
import org.springframework.data.domain.Page;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PaginatedResponse<T> {
    private List<T> content;
    private PageableDTO pageable;
    private int totalPages;
    private long totalElements;
    private boolean last;
    private boolean first;
    private SortInfo sort;
    private int numberOfElements;
    private int size;
    private int number;
    private boolean empty;

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class PageableDTO {
        private int pageNumber;
        private int pageSize;
        private long offset;
        private boolean paged;
        private boolean unpaged;
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class SortInfo {
        private boolean sorted;
        private boolean unsorted;
        private boolean empty;
    }

    public static <T> PaginatedResponse<T> from(Page<T> page) {
        return PaginatedResponse.<T>builder()
                .content(page.getContent())
                .pageable(PageableDTO.builder()
                        .pageNumber(page.getNumber())
                        .pageSize(page.getSize())
                        .offset(page.getPageable().getOffset())
                        .paged(page.getPageable().isPaged())
                        .unpaged(page.getPageable().isUnpaged())
                        .build())
                .totalPages(page.getTotalPages())
                .totalElements(page.getTotalElements())
                .last(page.isLast())
                .first(page.isFirst())
                .sort(SortInfo.builder()
                        .sorted(page.getSort().isSorted())
                        .unsorted(page.getSort().isUnsorted())
                        .empty(page.getSort().isEmpty())
                        .build())
                .numberOfElements(page.getNumberOfElements())
                .size(page.getSize())
                .number(page.getNumber())
                .empty(page.isEmpty())
                .build();
    }
}