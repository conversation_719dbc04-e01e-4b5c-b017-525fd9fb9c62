package org.example.cts.currencyservice.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.List;

@RefreshScope
@Getter
@Setter
@ConfigurationProperties(prefix = "binance")
public class CloudProperties {
    private List<String> pairs;
}
