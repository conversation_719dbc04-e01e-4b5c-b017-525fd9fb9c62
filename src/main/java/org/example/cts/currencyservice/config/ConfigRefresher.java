package org.example.cts.currencyservice.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.cts.currencyservice.service.PairsService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cloud.context.refresh.ContextRefresher;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Set;

@Component
@Slf4j
@RequiredArgsConstructor
public class ConfigRefresher {

    @Qualifier("configDataContextRefresher")
    private final ContextRefresher contextRefresher;

    private final PairsService pairsService;

    @Scheduled(cron = "${config.refresh.cron:0 0 0 * * *}")
    public void refreshConfig() {
        log.debug("Checking for configuration updates...");
        Set<String> updatedKeys = contextRefresher.refresh();

        if (!updatedKeys.isEmpty()) {
            pairsService.filterMissingPairs();
            log.info("Configuration updated for keys: {}", updatedKeys);
        } else {
            log.debug("No configuration changes detected");
        }
    }
}
