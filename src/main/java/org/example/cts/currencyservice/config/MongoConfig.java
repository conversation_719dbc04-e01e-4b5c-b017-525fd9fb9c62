package org.example.cts.currencyservice.config;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.CreateCollectionOptions;
import com.mongodb.client.model.TimeSeriesOptions;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;

@Configuration
@RequiredArgsConstructor
public class MongoConfig {

    private final MongoClient mongoClient;

    private final MongoTemplate mongoTemplate;

    @Value("${spring.data.mongodb.database}")
    protected String databaseName;

    @PostConstruct
    public void initializeTimeSeriesCollection() {
        MongoDatabase database = mongoClient.getDatabase(databaseName);

        boolean collectionExists = mongoTemplate.collectionExists("exchange_rates");

        if (!collectionExists) {
            TimeSeriesOptions timeSeriesOptions = new TimeSeriesOptions("timestamp")
                    .metaField("pairs")
                    .granularity(com.mongodb.client.model.TimeSeriesGranularity.SECONDS);

            CreateCollectionOptions options = new CreateCollectionOptions()
                    .timeSeriesOptions(timeSeriesOptions);

            database.createCollection("exchange_rates", options);

            System.out.println("Time series collection 'exchange_rates' created successfully");
        }
    }
}
