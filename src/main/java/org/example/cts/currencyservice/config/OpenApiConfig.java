package org.example.cts.currencyservice.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class OpenApiConfig {

    @Value("${spring.application.name}")
   protected String applicationName;

    @Bean
    public OpenAPI currencyGatewayOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title(applicationName + " API")
                        .description("Currency service API")
                        .version("v1.0.0")
                        .contact(new Contact()
                                .name("CTS")))
                .servers(List.of(
                        new Server()
                                .url("/")
                                .description("Server URL")
                ));
    }
}
