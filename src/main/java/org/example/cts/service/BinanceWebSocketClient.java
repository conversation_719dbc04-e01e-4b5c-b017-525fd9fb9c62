package org.example.cts.service;

import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;

import java.net.URI;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
public class BinanceWebSocketClient extends WebSocketClient {

    private final BinanceService binanceService;
    private final AtomicBoolean reconnecting = new AtomicBoolean(false);
    private final AtomicBoolean isClosed = new AtomicBoolean(false);
    private final ScheduledExecutorService reconnectExecutor;

    private int reconnectAttempts = 0;
    private final int initialReconnectDelay = 1000;
    private final int maxReconnectDelay = 60000;
    private int currentReconnectDelay = initialReconnectDelay;

    public BinanceWebSocketClient(URI serverUri, BinanceService binanceService, ScheduledExecutorService executor) {
        super(serverUri);
        this.binanceService = binanceService;
        this.reconnectExecutor = executor;
    }

    @Override
    public void onOpen(ServerHandshake serverHandshake) {
        reconnectAttempts = 0;
        currentReconnectDelay = initialReconnectDelay;
    }

    @Override
    public void onMessage(String message) {
        try {
            binanceService.processMessage(message);
        } catch (Exception e) {
            log.error("Error processing WebSocket message: {}", e.getMessage(), e);
        }
    }

    @Override
    public void onClose(int code, String reason, boolean remote) {
        if (!isClosed.get()) {
            attemptReconnect();
        }
    }

    @Override
    public void onError(Exception e) {
        log.error("Error in Binance WebSocket API: {}", e.getMessage(), e);
        if (!reconnecting.get() && !this.isOpen()) {
            attemptReconnect();
        }
    }

    private void attemptReconnect() {
        if (reconnecting.compareAndSet(false, true)) {
            int maxReconnectAttempts = 10;
            if (reconnectAttempts >= maxReconnectAttempts) {
                log.error("Maximum reconnection attempts ({}) reached for {}. Giving up.",
                        maxReconnectAttempts, getURI());
                reconnecting.set(false);
                return;
            }

            reconnectAttempts++;

            int jitter = (int) (currentReconnectDelay * 0.1 * Math.random());
            int delay = currentReconnectDelay + jitter;

            log.info("Scheduling reconnect attempt {} in {} ms for {}",
                    reconnectAttempts, delay, getURI());

            reconnectExecutor.schedule(() -> {
                try {
                    if (!this.isOpen()) {
                        log.info("Attempting to reconnect to {}", getURI());
                        reconnect();
                    }
                } catch (Exception e) {
                    log.error("Failed to reconnect to {}: {}", getURI(), e.getMessage(), e);
                    currentReconnectDelay = Math.min(currentReconnectDelay * 2, maxReconnectDelay);
                    attemptReconnect();
                } finally {
                    reconnecting.set(false);
                }
            }, delay, TimeUnit.MILLISECONDS);
        }
    }

    @Override
    public void close() {
        isClosed.set(true);
        reconnectExecutor.shutdownNow();
        super.close();
    }
}