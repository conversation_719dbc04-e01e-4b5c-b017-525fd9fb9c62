package org.example.cts.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.cts.config.GatewayConfig;
import org.example.cts.dto.CryptoPriceEvent;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.net.URI;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
@Slf4j
@RequiredArgsConstructor
public class BinanceService {

    private final ObjectMapper objectMapper;
    private final GatewayConfig gatewayConfig;
    private Set<String> currentPairs = new HashSet<>();
    private final ReadWriteLock pairsLock = new ReentrantReadWriteLock();
    private final Map<String, BinanceWebSocketClient> streamToClientMap = new ConcurrentHashMap<>();
    private final Set<BinanceWebSocketClient> activeConnections = ConcurrentHashMap.newKeySet();
    private final KafkaProducerService kafkaProducerService;
    ScheduledExecutorService executor = new ScheduledThreadPoolExecutor(1);

    private static final String JSON_KEY_SYMBOL = "s";
    private static final String JSON_KEY_PRICE = "c";
    private static final String JSON_KEY_VOLUME = "v";
    private static final String JSON_KEY_TIMESTAMP = "E";
    private static final String JSON_KEY_DATA = "data";
    private static final String TICKER_STREAM_SUFFIX = "@ticker";

    @Value("${websocket.client.connection.lost.timeout:30}")
    private int clientConnectionLostTimeout;

    @Value("${websocket.max.pairs.per.connection:200}")
    private int maxPairsPerConnection;

    @Value("${websocket.binance.base.url:wss://stream.binance.com:9443/stream?streams=}")
    private String binanceBaseUrl;

    @PostConstruct
    public void init() {
        refreshPairs();
    }

    @PreDestroy
    public void cleanup() {
        activeConnections.forEach(client -> {
            try {
                client.close();
            } catch (Exception e) {
                log.warn("Error closing WebSocket connection: {}", e.getMessage());
            }
        });
        activeConnections.clear();
        streamToClientMap.clear();
    }

    @EventListener(RefreshScopeRefreshedEvent.class)
    public void onRefresh() {
        Set<String> newPairs = new HashSet<>(gatewayConfig.getPairs());
        if (!newPairs.equals(currentPairs)) {
            log.info("Pairs changed in config. Updating connections...");
            refreshPairs();
        }
    }

    public void refreshPairs() {
        Set<String> newPairs = new HashSet<>(gatewayConfig.getPairs() != null ? gatewayConfig.getPairs() : Set.of());
        log.info("Refreshing pairs: {}", newPairs);

        pairsLock.writeLock().lock();
        try {
            if (newPairs.equals(currentPairs)) {
                log.info("No change in pairs after acquiring lock");
                return;
            }

            log.info("Pairs changed. Updating connections...");

            Set<String> pairsToRemove = currentPairs.stream()
                    .filter(pair -> !newPairs.contains(pair))
                    .collect(Collectors.toSet());

            log.info("Pairs to remove: {}", pairsToRemove);

            Set<String> pairsToAdd = newPairs.stream()
                    .filter(pair -> !currentPairs.contains(pair))
                    .collect(Collectors.toSet());

            log.info("Pairs to add: {}", pairsToAdd);

            pairsToRemove.forEach(this::disconnectFromPair);

            connectToPairs(pairsToAdd);

            currentPairs = newPairs;
            log.info("Pairs refresh completed successfully");
        } finally {
            pairsLock.writeLock().unlock();
            log.info("Released write lock");
        }
    }

    void connectToPairs(Set<String> pairs) {
        if (pairs.isEmpty()) {
            log.info("No new pairs to connect");
            return;
        }

        List<List<String>> batches = partitionList(new ArrayList<>(pairs));
        batches.forEach(batch -> {
            try {
                String streams = batch.stream()
                        .map(pair -> pair.toLowerCase() + TICKER_STREAM_SUFFIX)
                        .collect(Collectors.joining("/"));

                URI uri = new URI(binanceBaseUrl + streams);
                BinanceWebSocketClient client = new BinanceWebSocketClient(uri, this, executor);
                client.setConnectionLostTimeout(clientConnectionLostTimeout);
                client.connect();

                activeConnections.add(client);
                batch.forEach(pair -> streamToClientMap.put(pair, client));
                log.info("Connected batch of {} pairs", batch.size());
            } catch (Exception e) {
                log.error("Error connecting batch", e);
            }
        });
    }

    private <T> List<List<T>> partitionList(List<T> list) {
        if (list.isEmpty()) {
            return Collections.emptyList();
        }

        return IntStream.range(0, (list.size() + maxPairsPerConnection - 1) / maxPairsPerConnection)
                .mapToObj(i -> list.subList(i * maxPairsPerConnection, Math.min(list.size(), (i + 1) * maxPairsPerConnection)))
                .collect(Collectors.toList());
    }

    private void disconnectFromPair(String pair) {
        BinanceWebSocketClient client = streamToClientMap.remove(pair);
        if (client != null) {
            boolean isUsedByOthers = streamToClientMap.values().stream()
                    .anyMatch(c -> c.equals(client));

            if (!isUsedByOthers) {
                client.close();
                activeConnections.remove(client);
                log.info("Closed connection as it's no longer used");
            }
            log.info("Disconnected from pair: {}", pair);
        }
    }

    public void processMessage(String message) {
        try {
            JsonNode rootNode = objectMapper.readTree(message);
            JsonNode dataNode = rootNode.path(JSON_KEY_DATA);

            if (dataNode.isMissingNode()) {
                log.warn("Received message with missing data node: {}", message);
                return;
            }

            String symbol = dataNode.path(JSON_KEY_SYMBOL).asText();

            if (symbol == null || symbol.isEmpty() ||
                    !dataNode.has(JSON_KEY_PRICE) || !dataNode.has(JSON_KEY_VOLUME) || !dataNode.has(JSON_KEY_TIMESTAMP)) {
                log.warn("Received incomplete data for symbol: {}", symbol);
                return;
            }

            BigDecimal price = new BigDecimal(dataNode.path(JSON_KEY_PRICE).asText());
            BigDecimal volume = new BigDecimal(dataNode.path(JSON_KEY_VOLUME).asText());
            Instant timestamp = Instant.ofEpochMilli(dataNode.path(JSON_KEY_TIMESTAMP).asLong());

            CryptoPriceEvent event = new CryptoPriceEvent(symbol, price, volume, timestamp);

            try {
                String json = objectMapper.writeValueAsString(event);
                kafkaProducerService.sendCryptoPriceEvent(gatewayConfig.getKafkaTopicSendPriceUpdate(), json);
                log.debug("Sent to Kafka: {}", symbol);
            } catch (Exception e) {
                log.error("Error sending data to Kafka for symbol {}: {}", symbol, e.getMessage());
            }

        } catch (Exception e) {
            log.error("Error processing message: {}", e.getMessage());
        }
    }
}