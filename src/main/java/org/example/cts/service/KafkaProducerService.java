package org.example.cts.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import java.util.concurrent.CompletableFuture;

@Service
@RequiredArgsConstructor
@Slf4j
public class KafkaProducerService {

    private final KafkaTemplate<String, String> kafkaTemplate;

    @Retryable(
            backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public void sendCryptoPriceEvent(String topic, String value) {
        ProducerRecord<String, String> record = new ProducerRecord<>(topic, value);

        CompletableFuture<SendResult<String, String>> future = kafkaTemplate.send(record);

        future.whenComplete((result, ex) -> {
            if (ex == null) {
                log.debug("Sent message to topic {}: {}", topic, value);
            } else {
                log.error("Unable to send message to topic {}: {}", topic, ex.getMessage());
            }
        });
    }
}
