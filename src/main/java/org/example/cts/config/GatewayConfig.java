package org.example.cts.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.List;

@RefreshScope
@Getter
@Setter
@ConfigurationProperties(prefix = "binance")
public class GatewayConfig {
    private List<String> pairs;
    private String kafkaTopicSendPriceUpdate;
}