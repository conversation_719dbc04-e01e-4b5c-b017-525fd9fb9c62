package org.example.cts.config;

import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.TopicBuilder;

@Configuration
public class KafkaTopicConfig {

    @Value("${kafka.topic.input}")
    private String inputTopicName;

    @Value("${kafka.topic.output}")
    private String outputTopicName;

    @Bean
    public NewTopic ingressTopic() {
        return TopicBuilder.name(inputTopicName).build();
    }

    @Bean
    public NewTopic outputTopic() {
        return TopicBuilder.name(outputTopicName).build();
    }
}

