package org.example.cts;

import org.example.cts.config.GatewayConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableScheduling
@EnableConfigurationProperties(GatewayConfig.class)
public class CurrencyGateway {

	public static void main(String[] args) {
		SpringApplication.run(CurrencyGateway.class, args);
	}
}
