package org.example.cts.walletservice.model;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class WalletBalance {

    @Id
    @EqualsAndHashCode.Include
    private UUID id;

    private UUID currencyId;

    private BigDecimal balance;

    private LocalDateTime updatedAt;

    @ManyToOne
    @JoinColumn(name = "wallet_id")
    private UserWallet userWallet;
}