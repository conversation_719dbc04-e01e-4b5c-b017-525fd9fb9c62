package org.example.cts.walletservice.model;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class UserWallet {

    @Id
    @EqualsAndHashCode.Include
    private UUID id;

    private UUID userId;

    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    @OneToMany(mappedBy = "userWallet", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<WalletBalance> balances;

    @OneToMany(mappedBy = "userWallet", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<WalletTransaction> transactions;
}