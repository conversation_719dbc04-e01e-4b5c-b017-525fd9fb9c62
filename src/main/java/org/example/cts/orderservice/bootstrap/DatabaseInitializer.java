package org.example.cts.orderservice.bootstrap;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.cts.orderservice.model.Order;
import org.example.cts.orderservice.model.enumType.OrderStatus;
import org.example.cts.orderservice.model.enumType.OrderType;
import org.example.cts.orderservice.model.enumType.Side;
import org.example.cts.orderservice.repository.OrderRepository;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

@Component
@RequiredArgsConstructor
@Slf4j
@Profile({"dev"})
public class DatabaseInitializer {

    private final OrderRepository orderRepository;

    @PostConstruct
    public void initDatabase() {
        if (orderRepository.count() > 0) {
            log.info("Database already contains data, skipping initialization");
            return;
        }

        log.info("Initializing database with sample orders");
        
        // Create some fixed user IDs for sample data
        UUID user1 = UUID.fromString("11111111-1111-1111-1111-111111111111");
        UUID user2 = UUID.fromString("*************-2222-2222-************");
        UUID user3 = UUID.fromString("*************-3333-3333-************");
        
        LocalDateTime now = LocalDateTime.now();
        
        // Create sample orders
        List<Order> orders = Arrays.asList(
            // User 1 orders
            Order.builder()
                .id(UUID.randomUUID())
                .userId(user1)
                .tradingPair("BTC/USDT")
                .orderType(OrderType.LIMIT)
                .side(Side.BUY)
                .amount(new BigDecimal("0.5"))
                .filledAmount(new BigDecimal("0.5"))
                .price(new BigDecimal("42000.00"))
                .status(OrderStatus.FILLED)
                .createdAt(now.minusDays(5))
                .updatedAt(now.minusDays(5).plusHours(2))
                .build(),
                
            Order.builder()
                .id(UUID.randomUUID())
                .userId(user1)
                .tradingPair("ETH/USDT")
                .orderType(OrderType.MARKET)
                .side(Side.SELL)
                .amount(new BigDecimal("2.0"))
                .filledAmount(new BigDecimal("2.0"))
                .price(new BigDecimal("2500.00"))
                .status(OrderStatus.FILLED)
                .createdAt(now.minusDays(3))
                .updatedAt(now.minusDays(3))
                .build(),
                
            Order.builder()
                .id(UUID.randomUUID())
                .userId(user1)
                .tradingPair("SOL/USDT")
                .orderType(OrderType.LIMIT)
                .side(Side.BUY)
                .amount(new BigDecimal("10.0"))
                .filledAmount(BigDecimal.ZERO)
                .price(new BigDecimal("80.00"))
                .status(OrderStatus.OPEN)
                .createdAt(now.minusHours(12))
                .updatedAt(now.minusHours(12))
                .build(),
                
            // User 2 orders
            Order.builder()
                .id(UUID.randomUUID())
                .userId(user2)
                .tradingPair("BTC/USDT")
                .orderType(OrderType.LIMIT)
                .side(Side.SELL)
                .amount(new BigDecimal("0.75"))
                .filledAmount(new BigDecimal("0.5"))
                .price(new BigDecimal("43500.00"))
                .status(OrderStatus.OPEN)
                .createdAt(now.minusDays(1))
                .updatedAt(now.minusHours(6))
                .build(),
                
            Order.builder()
                .id(UUID.randomUUID())
                .userId(user2)
                .tradingPair("DOT/USDT")
                .orderType(OrderType.MARKET)
                .side(Side.BUY)
                .amount(new BigDecimal("50.0"))
                .filledAmount(new BigDecimal("50.0"))
                .price(new BigDecimal("6.85"))
                .status(OrderStatus.FILLED)
                .createdAt(now.minusDays(7))
                .updatedAt(now.minusDays(7))
                .build(),
                
            // User 3 orders
            Order.builder()
                .id(UUID.randomUUID())
                .userId(user3)
                .tradingPair("XRP/USDT")
                .orderType(OrderType.LIMIT)
                .side(Side.BUY)
                .amount(new BigDecimal("1000.0"))
                .filledAmount(BigDecimal.ZERO)
                .price(new BigDecimal("0.55"))
                .status(OrderStatus.CANCELLED)
                .createdAt(now.minusDays(2))
                .updatedAt(now.minusDays(1))
                .build(),
                
            Order.builder()
                .id(UUID.randomUUID())
                .userId(user3)
                .tradingPair("ADA/USDT")
                .orderType(OrderType.MARKET)
                .side(Side.SELL)
                .amount(new BigDecimal("500.0"))
                .filledAmount(new BigDecimal("500.0"))
                .price(new BigDecimal("0.38"))
                .status(OrderStatus.FILLED)
                .createdAt(now.minusDays(4))
                .updatedAt(now.minusDays(4))
                .build(),
                
            Order.builder()
                .id(UUID.randomUUID())
                .userId(user3)
                .tradingPair("AVAX/USDT")
                .orderType(OrderType.LIMIT)
                .side(Side.BUY)
                .amount(new BigDecimal("20.0"))
                .filledAmount(BigDecimal.ZERO)
                .price(new BigDecimal("32.50"))
                .status(OrderStatus.OPEN)
                .createdAt(now.minusHours(5))
                .updatedAt(now.minusHours(5))
                .build()
        );
        
        orderRepository.saveAll(orders);
        log.info("Database initialized with {} sample orders", orders.size());
    }
}
