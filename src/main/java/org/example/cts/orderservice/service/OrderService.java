package org.example.cts.orderservice.service;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.example.auth.AllUserDetails;
import org.example.cts.orderservice.dto.OrderDto;
import org.example.cts.orderservice.model.Order;
import org.example.cts.orderservice.model.enumType.OrderStatus;
import org.example.cts.orderservice.repository.OrderRepository;
import org.modelmapper.ModelMapper;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.security.access.AuthorizationServiceException;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

@Component
@RequiredArgsConstructor
public class OrderService {
    private final OrderRepository orderRepository;
    private final ModelMapper modelMapper;
    private final SimpMessagingTemplate messagingTemplate;

    public List<OrderDto> getAllOrdersForUser(UUID userId) {
        return orderRepository.findAllByUserId(userId)
                .stream()
                .map(order -> modelMapper.map(order, OrderDto.class))
                .toList();
    }

    private OrderDto updateOrder(UUID orderId, @Valid OrderDto orderDto, AllUserDetails userDetails) {
        Order updatedOrder = orderRepository.findById(orderId.toString())
                .map(order -> orderRepository.save(modelMapper.map(orderDto, Order.class)))
                .orElseThrow(() -> new IllegalArgumentException("Order not found"));

        messagingTemplate.convertAndSendToUser(
                userDetails.getUsername(),
                "/topic/orders",
                updatedOrder
        );

        messagingTemplate.convertAndSend(
                "/topic/orders/" + orderId,
                updatedOrder
        );

        return modelMapper.map(updatedOrder, OrderDto.class);
    }

    public OrderDto cancelOrder(UUID orderId, AllUserDetails userDetails) {
        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new IllegalArgumentException("Order not found"));

        if (!userDetails.getId().equals(order.getUserId()))
            throw new AuthorizationServiceException("You are not authorized to cancel this order");

        order.setStatus(OrderStatus.CANCELLED);
        orderRepository.save(order);
        messagingTemplate.convertAndSendToUser(
                userDetails.getUsername(),
                "/me/orders",
                order
        );

        return modelMapper.map(order, OrderDto.class);
    }
}
