package org.example.cts.orderservice.repository;

import org.example.cts.orderservice.model.Order;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface OrderRepository extends MongoRepository<Order, String> {
    List<Order> findAllByUserId(UUID userId);

    Optional<Order> findById(UUID id);
}
