package org.example.cts.orderservice.config;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;
import org.springframework.messaging.converter.MappingJackson2MessageConverter;
import org.springframework.messaging.converter.MessageConverter;

import java.util.List;

import static org.example.cts.orderservice.config.AppConfig.BASE_URL;

@Configuration
@RequiredArgsConstructor
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

    @Value("${websocket.topic.prefix}")
    private String topicPrefix;

    @Value("${websocket.endpoint}")
    private String endpoint;

    @Value("${websocket.destination.prefix}")
    private String destinationPrefix;

    @Value("${websocket.user.destination.prefix}")
    private String userDestinationPrefix;

    private final MappingJackson2MessageConverter mappingJackson2MessageConverter;

    @Override
    public void configureMessageBroker(MessageBrokerRegistry registry) {
        registry.enableSimpleBroker(topicPrefix);
        registry.setApplicationDestinationPrefixes(destinationPrefix);
        registry.setUserDestinationPrefix(userDestinationPrefix);
    }

    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        registry.addEndpoint(BASE_URL + endpoint)
                .setAllowedOrigins("*");

        registry.addEndpoint(BASE_URL + endpoint)
                .setAllowedOrigins("*")
                .withSockJS();
    }

    @Override
    public boolean configureMessageConverters(List<MessageConverter> messageConverters) {
        messageConverters.clear();
        messageConverters.add(mappingJackson2MessageConverter);
        return true;
    }
}
