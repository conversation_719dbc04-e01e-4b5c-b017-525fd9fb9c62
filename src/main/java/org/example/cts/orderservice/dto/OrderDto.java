package org.example.cts.orderservice.dto;

import lombok.*;
import org.example.cts.orderservice.model.enumType.OrderStatus;
import org.example.cts.orderservice.model.enumType.OrderType;
import org.example.cts.orderservice.model.enumType.Side;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Getter
@Setter
public class OrderDto {
    private UUID id;

    @NotNull(message = "User ID cannot be null")
    private UUID userId;

    @NotBlank(message = "Trading pair cannot be empty")
    private String tradingPair;

    @NotNull(message = "Order type cannot be null")
    private OrderType orderType;

    @NotNull(message = "Side cannot be null")
    private Side side;

    @NotNull(message = "Amount cannot be null")
    @Positive(message = "Amount must be positive")
    private BigDecimal amount;

    private BigDecimal filledAmount;

    @Positive(message = "Price must be positive")
    private BigDecimal price;

    private OrderStatus status;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;
}
