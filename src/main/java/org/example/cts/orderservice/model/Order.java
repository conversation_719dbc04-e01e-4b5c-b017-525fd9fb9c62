package org.example.cts.orderservice.model;

import lombok.*;
import org.example.cts.orderservice.model.enumType.OrderStatus;
import org.example.cts.orderservice.model.enumType.OrderType;
import org.example.cts.orderservice.model.enumType.Side;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "orders")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Order {

    @Id
    private UUID id;

    private UUID userId;

    private String tradingPair;

    private OrderType orderType;

    private Side side;

    private BigDecimal amount;

    private BigDecimal filledAmount;

    private BigDecimal price;

    private OrderStatus status;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;
}
