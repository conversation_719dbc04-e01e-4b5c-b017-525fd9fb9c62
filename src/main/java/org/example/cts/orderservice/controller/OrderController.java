package org.example.cts.orderservice.controller;

import lombok.RequiredArgsConstructor;
import org.example.auth.AllUserDetails;
import org.example.cts.orderservice.config.AppConfig;
import org.example.cts.orderservice.dto.OrderDto;
import org.example.cts.orderservice.service.OrderService;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.simp.annotation.SubscribeMapping;
import org.springframework.stereotype.Controller;

import java.util.List;
import java.util.UUID;

@Controller
@RequiredArgsConstructor
@MessageMapping(AppConfig.BASE_URL)
public class OrderController {
    private final OrderService orderService;

    /**
     * WebSocket endpoint for clients to subscribe to their orders
     */
    @SubscribeMapping("/users/my")
    public List<OrderDto> getAllOrders(SimpMessageHeaderAccessor headerAccessor) {
        return orderService.getAllOrdersForUser(UUID.fromString("*************-2222-2222-************"));
    }

    @MessageMapping("{orderId}/cancel")
    public void cancelOrder(
            @DestinationVariable UUID orderId) {

        orderService.cancelOrder(orderId, new AllUserDetails(
                UUID.fromString("*************-2222-2222-************"),
                "test",
                "test",
                "test",
                List.of()
        ));
    }
}
