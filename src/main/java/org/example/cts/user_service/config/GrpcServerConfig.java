package org.example.cts.user_service.config;

import org.example.cts.user_service.service.impl.UserServiceImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GrpcServerConfig {

    @Value("${grpc.server.port:9090}")
    private int grpcPort;

    @Bean
    public NettyGrpcServerBuilder grpcServerBuilder() {
        return NettyGrpcServerBuilder.forPort(grpcPort);
    }

    @Bean
    public GrpcServer grpcServer(NettyGrpcServerBuilder serverBuilder, UserServiceImpl userService) {
        return serverBuilder
                .addService(userService)
                .build();
    }
}
