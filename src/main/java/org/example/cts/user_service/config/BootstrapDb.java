package org.example.cts.user_service.config;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.example.cts.user_service.model.User;
import org.example.cts.user_service.model.enums.Role;
import org.example.cts.user_service.repository.UserRepository;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

/**
 * Bootstrap class that populates the database with initial data when the application starts.
 * This component runs only in the "dev" profile.
 */
@Component
@Profile("dev")
@RequiredArgsConstructor
@Slf4j
public class BootstrapDb implements CommandLineRunner {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    @Override
    @Transactional
    public void run(String... args) {
        log.info("Loading bootstrap data for development environment...");

        createUserIfNotExists(
                "<EMAIL>",
                passwordEncoder.encode("user123"),
                Role.ROLE_USER
        );
    }

    private void createUserIfNotExists(String email, String password, Role role) {
        Optional<User> existingUser = userRepository.findByUsername(email);
        if (existingUser.isPresent()) {
            log.info("User {} already exists, skipping creation", email);
            return;
        }

        User newUser = User.builder()
                .username(email)
                .password(password)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .roles(Set.of(role))
                .build();

        userRepository.save(newUser);
        log.info("Created user: {}", newUser);
    }
}