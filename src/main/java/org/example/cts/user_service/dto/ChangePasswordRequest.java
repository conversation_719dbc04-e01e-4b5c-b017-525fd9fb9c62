package org.example.cts.user_service.dto;

import static org.example.cts.user_service.config.ValidationConstants.MIN_PASSWORD_LENGTH;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

public record ChangePasswordRequest(
        @NotNull(message = "Old password mush be not null")
        String oldPassword,
        @NotNull(message = "New password mush be not null")
        @Min(value = 8, message = "Password must be at least " + MIN_PASSWORD_LENGTH + " characters long")
        String newPassword
) {
}
