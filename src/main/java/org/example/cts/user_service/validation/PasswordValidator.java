package org.example.cts.user_service.validation;

import static org.example.cts.user_service.config.ValidationConstants.MIN_PASSWORD_LENGTH;

import lombok.AllArgsConstructor;
import org.example.cts.user_service.exception.PasswordChangeNotAllowedException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class PasswordValidator {
    private final PasswordEncoder passwordEncoder;

    public void validatePasswordNotNull(String password) {
        if (password == null || password.isBlank()) {
            throw new PasswordChangeNotAllowedException("Password change not allowed: no password is set for this account");
        }
    }

    public void validateOldPassword(String oldPassword, String currentPassword) {
        if (!passwordEncoder.matches(oldPassword, currentPassword)) {
            throw new PasswordChangeNotAllowedException("Old password is incorrect");
        }
    }

    public void validateNewPassword(String newPassword, String currentPassword) {
        if (newPassword == null || newPassword.length() < MIN_PASSWORD_LENGTH) {
            throw new PasswordChangeNotAllowedException("Password must be at least " + MIN_PASSWORD_LENGTH + " characters long");
        }
        if (passwordEncoder.matches(newPassword, currentPassword)) {
            throw new PasswordChangeNotAllowedException("New password must be different from the old one");
        }
    }
}
