package org.example.cts.user_service.validation;

import java.util.UUID;
import lombok.AllArgsConstructor;
import org.example.cts.user_service.model.User;
import org.example.cts.user_service.repository.UserRepository;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class UserValidation {
    private final UserRepository userRepository;

    public User validateUserExistence(UUID id) {
        return userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("User with id " + id + " not found"));
    }
}
