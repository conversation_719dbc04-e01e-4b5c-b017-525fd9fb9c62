package org.example.cts.user_service.service.impl;

import java.time.LocalDateTime;
import java.util.UUID;

import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.cts.commonauth.AllUserDetails;
import org.cts.commonauth.service.TimeConverterService;
import org.example.cts.user_service.dto.ChangePasswordRequest;
import org.example.cts.user_service.exception.UserNotFoundException;
import org.example.cts.user_service.repository.UserRepository;
import org.example.cts.user_service.service.UserService;
import org.example.cts.user_service.validation.PasswordValidator;
import org.example.cts.user_service.validation.UserValidation;
import org.example.user.grpc.*;
import org.example.cts.user_service.model.User;
import org.example.cts.user_service.model.enums.Role;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.grpc.server.service.GrpcService;

@Slf4j
@Service
@GrpcService
@RequiredArgsConstructor
public class UserServiceImpl extends UserServiceGrpc.UserServiceImplBase implements UserService {
    private final UserRepository userRepository;
    private final PasswordValidator passwordValidator;
    private final UserValidation userValidation;
    private final PasswordEncoder passwordEncoder;
    private final TimeConverterService timeConverterService;

    @Transactional
    @Override
    public void delete(UUID id) {
        var user = userValidation.validateUserExistence(id);

        userRepository.delete(user);
    }

    @Transactional
    @Override
    public void changePassword(UUID id, ChangePasswordRequest changePasswordRequest) {
        var user = userValidation.validateUserExistence(id);

        passwordValidator.validatePasswordNotNull(user.getPassword());
        passwordValidator.validateOldPassword(changePasswordRequest.oldPassword(), user.getPassword());
        passwordValidator.validateNewPassword(changePasswordRequest.newPassword(), user.getPassword());

        user.setPassword(passwordEncoder.encode((changePasswordRequest.newPassword())));
        user.setUpdatedAt(LocalDateTime.now());
    }

    @Override
    public void loadUserByUsername(LoadUserByUsernameRequest request, StreamObserver<LoadUserByUsernameResponse> responseObserver) {
        try {
            log.info("Loading user by username: {}", request.getUsername());

            var user = userRepository.findByUsername(request.getUsername())
                    .orElseThrow(() -> new UserNotFoundException("User with username " + request.getUsername() + " not found"));

            UserDetails userDetails = UserDetails.newBuilder()
                    .setUserId(user.getId().toString())
                    .setUsername(user.getUsername())
                    .setPassword(user.getPassword())
                    .addAllRoles(user.getRoles() != null ? user.getRoles().stream().map(Role::name).toList() : java.util.List.of())
                    .setEnabled(true)
                    .build();

            responseObserver.onNext(LoadUserByUsernameResponse.newBuilder().setUserDetails(userDetails).build());
            responseObserver.onCompleted();
            log.info("Successfully loaded user by username: {}", request.getUsername());
        } catch (UserNotFoundException e) {
            log.warn("User not found: {}", request.getUsername());
            responseObserver.onError(io.grpc.Status.NOT_FOUND
                    .withDescription("User not found: " + request.getUsername())
                    .asRuntimeException());
        } catch (Exception e) {
            log.error("Error occurred while loading user by username: {}", e.getMessage(), e);
            responseObserver.onError(io.grpc.Status.INTERNAL
                    .withDescription("Internal server error: " + e.getMessage())
                    .asRuntimeException());
        }
    }

    @Override
    public void getUserDetailsById(GetUserDetailsByIdRequest request, StreamObserver<GetUserDetailsByIdResponse> responseObserver) {
        try {
            log.info("Getting user details by ID: {}", request.getUserId());

            UUID userId = UUID.fromString(request.getUserId());
            var user = userRepository.findById(userId)
                    .orElseThrow(() -> new UserNotFoundException("User with ID " + request.getUserId() + " not found"));

            UserDetails userDetails = UserDetails.newBuilder()
                    .setUserId(user.getId().toString())
                    .setUsername(user.getUsername())
                    .setPassword(user.getPassword())
                    .addAllRoles(user.getRoles() != null ? user.getRoles().stream().map(Role::name).toList() : java.util.List.of())
                    .setEnabled(true)
                    .build();

            responseObserver.onNext(GetUserDetailsByIdResponse.newBuilder().setUserDetails(userDetails).build());
            responseObserver.onCompleted();
            log.info("Successfully retrieved user details by ID: {}", request.getUserId());
        } catch (UserNotFoundException e) {
            log.warn("User not found with ID: {}", request.getUserId());
            responseObserver.onError(io.grpc.Status.NOT_FOUND
                    .withDescription("User not found with ID: " + request.getUserId())
                    .asRuntimeException());
        } catch (IllegalArgumentException e) {
            log.warn("Invalid user ID format: {}", request.getUserId());
            responseObserver.onError(io.grpc.Status.INVALID_ARGUMENT
                    .withDescription("Invalid user ID format: " + request.getUserId())
                    .asRuntimeException());
        } catch (Exception e) {
            log.error("Error occurred while getting user details by ID: {}", e.getMessage(), e);
            responseObserver.onError(io.grpc.Status.INTERNAL
                    .withDescription("Internal server error: " + e.getMessage())
                    .asRuntimeException());
        }
    }

    @Override
    public void registerUser(RegisterUserRequest request, StreamObserver<RegisterUserResponse> responseObserver) {
        try {
            log.info("Registering new user: {}", request.getUsername());

            // Check if user already exists
            if (userRepository.findByUsername(request.getUsername()).isPresent()) {
                log.warn("User already exists: {}", request.getUsername());
                responseObserver.onError(io.grpc.Status.ALREADY_EXISTS
                        .withDescription("User already exists: " + request.getUsername())
                        .asRuntimeException());
                return;
            }

            // Create new user
            User newUser = User.builder()
                    .username(request.getUsername())
                    .password(request.getPassword()) // Password should already be encoded by the client
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .roles(java.util.Set.of(Role.ROLE_USER))
                    .build();

            User savedUser = userRepository.save(newUser);

            RegisterUserResponse response = RegisterUserResponse.newBuilder()
                    .setSuccess(true)
                    .setUserId(savedUser.getId().toString())
                    .setMessage("User registered successfully")
                    .build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();
            log.info("Successfully registered user: {} with ID: {}", request.getUsername(), savedUser.getId());
        } catch (Exception e) {
            log.error("Error occurred while registering user: {}", e.getMessage(), e);
            responseObserver.onError(io.grpc.Status.INTERNAL
                    .withDescription("Internal server error: " + e.getMessage())
                    .asRuntimeException());
        }
    }
}
