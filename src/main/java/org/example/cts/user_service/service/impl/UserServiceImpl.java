package org.example.cts.user_service.service.impl;

import java.time.LocalDateTime;
import java.util.UUID;

import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.cts.commonauth.AllUserDetails;
import org.cts.commonauth.service.TimeConverterService;
import org.example.cts.user_service.dto.ChangePasswordRequest;
import org.example.cts.user_service.exception.UserNotFoundException;
import org.example.cts.user_service.repository.UserRepository;
import org.example.cts.user_service.service.UserService;
import org.example.cts.user_service.validation.PasswordValidator;
import org.example.cts.user_service.validation.UserValidation;
import org.example.user.grpc.LoadUserByUsernameRequest;
import org.example.user.grpc.LoadUserByUsernameResponse;
import org.example.user.grpc.UserDetails;
import org.example.user.grpc.UserServiceGrpc;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@AllArgsConstructor
public class UserServiceImpl extends UserServiceGrpc.UserServiceImplBase implements UserService {
    private final UserRepository userRepository;
    private final PasswordValidator passwordValidator;
    private final UserValidation userValidation;
    private final PasswordEncoder passwordEncoder;
    private final TimeConverterService timeConverterService;

    @Transactional
    @Override
    public void delete(UUID id) {
        var user = userValidation.validateUserExistence(id);

        userRepository.delete(user);
    }

    @Transactional
    @Override
    public void changePassword(UUID id, ChangePasswordRequest changePasswordRequest) {
        var user = userValidation.validateUserExistence(id);

        passwordValidator.validatePasswordNotNull(user.getPassword());
        passwordValidator.validateOldPassword(changePasswordRequest.oldPassword(), user.getPassword());
        passwordValidator.validateNewPassword(changePasswordRequest.newPassword(), user.getPassword());

        user.setPassword(passwordEncoder.encode((changePasswordRequest.newPassword())));
        user.setUpdatedAt(LocalDateTime.now());
    }

    @Override
    public void loadUserByUsername(LoadUserByUsernameRequest request, StreamObserver<LoadUserByUsernameResponse> responseObserver) {
        try {

            var user = userRepository.findByUsername(request.getUsername())
                    .orElseThrow(() -> new UserNotFoundException("User with username " + request.getUsername() + " not found"));

            UserDetails userDetails = UserDetails.newBuilder()
                    .setUserId(user.getId().toString())
                    .setUsername(user.getUsername())
                    .setPassword(user.getPassword())
                    .build();

            responseObserver.onNext(LoadUserByUsernameResponse.newBuilder().setUserDetails(userDetails).build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("Error occurred while loading user by username {}", e.getMessage());
            responseObserver.onError(e);
            throw e;
        }
    }
}
