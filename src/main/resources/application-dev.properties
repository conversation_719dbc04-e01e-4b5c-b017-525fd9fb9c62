spring.application.name=order-service

eureka.client.service-url.defaultZone=${EUREKA_SERVER_URL:http://localhost:8761/eureka}

spring.data.mongodb.uri=${MONGODB_URI:mongodb://localhost:27517/user_db}
spring.data.mongodb.uuid-representation=standard

client.url=http://localhost:4200

websocket.endpoint=/ws
websocket.topic.prefix=/topic
websocket.destination.prefix=/app
websocket.user.destination.prefix=/users