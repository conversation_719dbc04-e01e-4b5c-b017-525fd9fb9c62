spring.application.name=user-service
spring.liquibase.change-log=classpath:db.changelog/db.changelog-master.yaml
spring.liquibase.enabled=true

eureka.instance.nonSecurePort=${server.port:8080}

spring.config.import=optional:file:.env[.properties]

spring.datasource.url=${USER_DB_URL}
spring.datasource.username=${USER_DB_USER}
spring.datasource.password=${USER_DB_PASSWORD}

spring.jpa.hibernate.ddl-auto=validate

logging.level.org.springframework.security=DEBUG

logging.level.org.springframework.web=DEBUG

logging.level.org.example.authenticationservice.config.security=DEBUG
logging.level.org.example.authenticationservice.service=DEBUG

logging.level.web=DEBUG

jwt.public-key-path=classpath:keys/public.pem

spring.security.oauth2.resourceserver.jwt.issuer-uri=

server.port=8088
