spring.application.name=config-server

server.port=8888

spring.cloud.config.server.git.uri=https://gitlab.ctts.dev/crypto-trade-simulator/config-repo.git
spring.cloud.config.server.git.username=@herman.skapets
spring.cloud.config.server.git.password=**************************
spring.cloud.config.server.git.skip-ssl-validation=true
spring.cloud.config.server.git.clone-on-start=true

management.endpoints.web.exposure.include=health,info
management.endpoint.health.show-details=always