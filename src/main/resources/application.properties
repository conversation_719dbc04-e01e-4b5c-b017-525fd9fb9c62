spring.application.name=api-gateway

spring.config.import=optional:file:.env[.properties]

server.port=8080

# ROUTES

# Order Service
spring.cloud.gateway.routes[0].id=order-service
spring.cloud.gateway.routes[0].uri=lb://order-service
spring.cloud.gateway.routes[0].predicates[0]=Path=/v1/orders/**

# Wallet Service
spring.cloud.gateway.routes[1].id=wallet-service
spring.cloud.gateway.routes[1].uri=lb://wallet-service
spring.cloud.gateway.routes[1].predicates[0]=Path=/v1/wallets/**

# Currency Service
spring.cloud.gateway.routes[2].id=currency-service
spring.cloud.gateway.routes[2].uri=lb://currency-service
spring.cloud.gateway.routes[2].predicates[0]=Path=/v1/currencies/**

# Auth Service
spring.cloud.gateway.routes[3].id=auth-service
spring.cloud.gateway.routes[3].uri=lb://auth-service
spring.cloud.gateway.routes[3].predicates[0]=Path=/v1/auth/**

# Notification Service
spring.cloud.gateway.routes[4].id=notification-service
spring.cloud.gateway.routes[4].uri=lb://notification-service
spring.cloud.gateway.routes[4].predicates[0]=Path=/v1/notifications/**

# Eureka
eureka.client.register-with-eureka=false
eureka.client.fetch-registry=true
eureka.client.service-url.defaultZone=${EUREKA_SERVER_URL:http://eureka-server:8761/eureka}
