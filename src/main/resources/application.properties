spring.application.name=currency-gateway
spring.profiles.active=dev
server.port=8080

# cloud
spring.cloud.config.uri=http://config-management:8888
spring.cloud.config.label=${BRANCH_NAME:main}
spring.config.import=optional:configserver:
spring.cloud.config.fail-fast=true
spring.cloud.config.retry.initial-interval=1000
spring.cloud.config.retry.max-interval=5000
spring.cloud.config.retry.max-attempts=30

management.endpoints.web.exposure.include=refresh
management.endpoints.web.base-path=/actuator

# 00:00
config.refresh.cron=0 0 0 * * *

# Kafka
spring.kafka.bootstrap-servers=kafka:9092

# Consumer
spring.kafka.consumer.group-id=kafka-router

# Topics
kafka.topic.input=${KAFKA_TOPIC_INPUT:crypto.prices.update}
kafka.topic.output=${KAFKA_TOPIC_OUTPUT:crypto.prices.broadcast}

websocket.client.connection.lost.timeout=30
websocket.max.pairs.per.connection=200
websocket.binance.base.url=wss://stream.binance.com:9443/stream?streams=



