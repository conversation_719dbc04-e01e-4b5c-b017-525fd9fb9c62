spring.application.name=authentication-service

frontend.server.url=http://localhost:4200

server.port=8086

logging.level.org.springframework.security=DEBUG

logging.level.org.springframework.web=DEBUG

logging.level.org.example.authenticationservice.config.security=DEBUG
logging.level.org.example.authenticationservice.service=DEBUG

logging.level.web=DEBUG

spring.mvc.log-request-details=true

jwt.private-key-path=classpath:keys/private.pem
jwt.public-key-path=classpath:keys/public.pem
