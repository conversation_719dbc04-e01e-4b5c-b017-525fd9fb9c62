spring.application.name=currency-service

eureka.client.service-url.defaultZone=http://eureka-server:8761/eureka
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true
eureka.instance.prefer-ip-address=true
# This will help with service registration
eureka.instance.instance-id=${spring.application.name}:${random.uuid}

# Kafka
spring.kafka.bootstrap-servers=kafka:9092
kafka.topic.input=${KAFKA_TOPIC_INPUT:crypto.prices.broadcast}
spring.kafka.consumer.group-id=currency-consumer

# MongoDB
spring.data.mongodb.port=${MONGO_PORT:27017}
spring.data.mongodb.authentication-database=${MONGO_AUTH_DB:admin}
spring.data.mongodb.username=${MONGO_USERNAME:root}
spring.data.mongodb.password=${MONGO_PASSWORD:root}
spring.data.mongodb.host=${MONGO_HOST:currency-db}
spring.data.mongodb.database=${MONGO_DATABASE_NAME:currencyDB}
spring.data.mongodb.uuid-representation=standard

management.endpoints.web.exposure.include=refresh
management.endpoints.web.base-path=/actuator

# Cloud
spring.cloud.config.uri=http://config-management:8888
spring.cloud.config.label=${BRANCH_NAME:main}
spring.cloud.config.name=currency-gateway
spring.config.import=optional:configserver:
spring.cloud.config.fail-fast=true
spring.cloud.config.retry.initial-interval=1000
spring.cloud.config.retry.max-interval=5000
spring.cloud.config.retry.max-attempts=30

# Swagger
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true
springdoc.packagesToScan=org.example.cts.currencyservice.controller
springdoc.pathsToMatch=/**

client.url=http://localhost:4200

# 00:00
config.refresh.cron=0 0 0 * * *

# Binance
binance.pair.info.url=${BINANCE_PAIR_INFO_URL:https://api.binance.com/api/v3/exchangeInfo?symbol=}