spring.application.name=user-service
spring.liquibase.change-log=classpath:db.changelog/db.changelog-master.yaml
spring.liquibase.enabled=true

eureka.instance.nonSecurePort=${server.port:8080}

spring.config.import=optional:file:.env[.properties]

spring.datasource.url=${USER_DB_URL}
spring.datasource.username=${USER_DB_USER}
spring.datasource.password=${USER_DB_PASSWORD}

spring.jpa.hibernate.ddl-auto=validate

logging.level.org.springframework.web=DEBUG
logging.level.org.example.userservice=DEBUG

server.port=8088
