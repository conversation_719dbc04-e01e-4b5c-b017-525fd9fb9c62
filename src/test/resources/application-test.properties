# Application name
spring.application.name=order-service-test

# DataSource configuration
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=test
spring.datasource.password=test

# JPA configuration
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true

# MongoDB configuration
spring.data.mongodb.host=localhost
spring.data.mongodb.port=27017
spring.data.mongodb.database=order-service-test

# Spring Cloud configuration
spring.cloud.compatibility-verifier.enabled=false
spring.cloud.service-registry.auto-registration.enabled=false

# Eureka client configuration
eureka.client.enabled=false
eureka.client.register-with-eureka=false
eureka.client.fetch-registry=false