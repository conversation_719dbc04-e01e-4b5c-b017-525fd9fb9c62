package org.example.cts.currencyservice.service;

import org.example.cts.currencyservice.dto.BasePairsDto;
import org.example.cts.currencyservice.dto.PairsDetailsDto;
import org.example.cts.currencyservice.model.SupportedPair;
import org.example.cts.currencyservice.repository.SupportedPairRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SupportedPairServiceTest {

    @Mock
    private SupportedPairRepository supportedPairRepository;

    @Mock
    private MongoTemplate mongoTemplate;

    @InjectMocks
    private SupportedPairService supportedPairService;

    private SupportedPair supportedPair;
    private final UUID pairId = UUID.randomUUID();

    @BeforeEach
    void setUp() {
        supportedPair = SupportedPair.builder()
                .id(pairId)
                .baseCurrencyCode("BTC")
                .targetCurrencyCode("USDT")
                .pairSymbol("BTCUSDT")
                .minOrderAmount(new BigDecimal("10.0"))
                .feePercentage(new BigDecimal("0.1"))
                .build();
    }

    @Test
    void getPairs_WithoutSearch_ShouldReturnPaginatedResults() {
        int page = 0;
        int size = 10;
        String sort = "pairSymbol";
        String direction = "asc";
        String searchSymbol = "";

        List<SupportedPair> pairsList = List.of(supportedPair);

        when(mongoTemplate.find(any(Query.class), eq(SupportedPair.class))).thenReturn(pairsList);
        when(supportedPairRepository.count()).thenReturn(1L);

        Page<BasePairsDto> result = supportedPairService.getPairs(page, size, sort, direction, searchSymbol);

        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals("BTCUSDT", result.getContent().get(0).getPairSymbol());

        verify(mongoTemplate).find(any(Query.class), eq(SupportedPair.class));
        verify(supportedPairRepository).count();
    }

    @Test
    void getPairs_WithSearch_ShouldFilterBySymbol() {
        int page = 0;
        int size = 10;
        String sort = "pairSymbol";
        String direction = "asc";
        String searchSymbol = "BTC";

        List<SupportedPair> pairsList = List.of(supportedPair);

        when(mongoTemplate.find(any(Query.class), eq(SupportedPair.class))).thenReturn(pairsList);
        when(mongoTemplate.count(any(Query.class), eq(SupportedPair.class))).thenReturn(1L);

        Page<BasePairsDto> result = supportedPairService.getPairs(page, size, sort, direction, searchSymbol);

        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals("BTCUSDT", result.getContent().get(0).getPairSymbol());

        verify(mongoTemplate).find(any(Query.class), eq(SupportedPair.class));
        verify(mongoTemplate).count(any(Query.class), eq(SupportedPair.class));
        verify(supportedPairRepository, never()).count();
    }

    @Test
    void getPairs_WhenNoPairsFound_ShouldThrowNotFoundException() {
        int page = 0;
        int size = 10;
        String sort = "pairSymbol";
        String direction = "asc";
        String searchSymbol = "";

        when(mongoTemplate.find(any(Query.class), eq(SupportedPair.class))).thenReturn(new ArrayList<>());

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () ->
                supportedPairService.getPairs(page, size, sort, direction, searchSymbol)
        );

        assertEquals("404 NOT_FOUND \"Pairs not found\"", exception.getMessage());
        verify(mongoTemplate).find(any(Query.class), eq(SupportedPair.class));
    }

    @Test
    void getPairById_WhenPairExists_ShouldReturnPairDetails() {
        when(supportedPairRepository.findById(pairId)).thenReturn(Optional.of(supportedPair));

        PairsDetailsDto result = supportedPairService.getPairById(pairId);

        assertNotNull(result);
        assertEquals("BTCUSDT", result.getPairSymbol());
        assertEquals("BTC", result.getBaseCode());
        assertEquals("USDT", result.getTargetCode());
        assertEquals("10.0", result.getMinOrderAmount().toString());

        verify(supportedPairRepository).findById(pairId);
    }

    @Test
    void getPairById_WhenPairDoesNotExist_ShouldThrowNotFoundException() {
        UUID nonExistentId = UUID.randomUUID();
        when(supportedPairRepository.findById(nonExistentId)).thenReturn(Optional.empty());

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () ->
                supportedPairService.getPairById(nonExistentId)
        );

        assertEquals("404 NOT_FOUND \"Pair not found\"", exception.getMessage());
        verify(supportedPairRepository).findById(nonExistentId);
    }

    @Test
    void getPairByPairSymbol_WhenPairExists_ShouldReturnPairDetails() {
        String pairSymbol = "BTCUSDT";
        when(supportedPairRepository.findByPairSymbol(pairSymbol)).thenReturn(Optional.of(supportedPair));

        PairsDetailsDto result = supportedPairService.getPairByPairSymbol(pairSymbol);

        assertNotNull(result);
        assertEquals("BTCUSDT", result.getPairSymbol());
        assertEquals("BTC", result.getBaseCode());
        assertEquals("USDT", result.getTargetCode());
        assertEquals("10.0", result.getMinOrderAmount().toString());

        verify(supportedPairRepository).findByPairSymbol(pairSymbol);
    }

    @Test
    void getPairByPairSymbol_WhenPairDoesNotExist_ShouldThrowNotFoundException() {
        String nonExistentSymbol = "NONEXISTENT";
        when(supportedPairRepository.findByPairSymbol(nonExistentSymbol)).thenReturn(Optional.empty());

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () ->
                supportedPairService.getPairByPairSymbol(nonExistentSymbol)
        );

        assertEquals("404 NOT_FOUND \"Pair not found\"", exception.getMessage());
        verify(supportedPairRepository).findByPairSymbol(nonExistentSymbol);
    }
}