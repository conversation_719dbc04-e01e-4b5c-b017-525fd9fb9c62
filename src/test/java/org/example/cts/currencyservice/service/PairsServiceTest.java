package org.example.cts.currencyservice.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.example.cts.currencyservice.config.CloudProperties;
import org.example.cts.currencyservice.dto.FilterInfo;
import org.example.cts.currencyservice.dto.PairInfo;
import org.example.cts.currencyservice.dto.SymbolInfo;
import org.example.cts.currencyservice.model.SupportedPair;
import org.example.cts.currencyservice.repository.SupportedPairRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PairsServiceTest {

    @Mock
    private CloudProperties cloudProperties;

    @Mock
    private SupportedPairRepository supportedPairRepository;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private RestTemplate restTemplate;

    @InjectMocks
    private PairsService pairsService;

    @Captor
    private ArgumentCaptor<List<SupportedPair>> pairsCaptor;

    private JsonNode rootNode;
    private List<String> configuredPairs;
    private List<SupportedPair> existingPairs;

    @BeforeEach
    void setUp() {
        String binancePairInfoUrl = "https://api.binance.com/api/v3/exchangeInfo?symbol=";
        ReflectionTestUtils.setField(pairsService, "binancePairInfoUrl", binancePairInfoUrl);

        configuredPairs = Arrays.asList("BTCUSDT", "ETHUSDT", "ADAUSDT");

        existingPairs = new ArrayList<>();
        SupportedPair btcPair = SupportedPair.builder()
                .id(UUID.randomUUID())
                .baseCurrencyCode("BTC")
                .targetCurrencyCode("USDT")
                .pairSymbol("BTCUSDT")
                .minOrderAmount(new BigDecimal("10.0"))
                .feePercentage(new BigDecimal("0.1"))
                .build();
        existingPairs.add(btcPair);

        ObjectMapper realMapper = new ObjectMapper();
        ObjectNode mockRootNode = realMapper.createObjectNode();
        ArrayNode mockSymbolsNode = mockRootNode.putArray("symbols");

        ObjectNode symbolNode = mockSymbolsNode.addObject();
        symbolNode.put("symbol", "ETHUSDT");
        symbolNode.put("baseAsset", "ETH");
        symbolNode.put("quoteAsset", "USDT");

        ArrayNode filtersArray = symbolNode.putArray("filters");
        ObjectNode filterNode = filtersArray.addObject();
        filterNode.put("filterType", "NOTIONAL");
        filterNode.put("minNotional", "10.00000000");

        rootNode = mockRootNode;
    }

    @Test
    void filterMissingPairs_WithConfiguredPairs_ShouldFetchMissingPairs() {
        when(cloudProperties.getPairs()).thenReturn(configuredPairs);
        when(supportedPairRepository.findAllByPairSymbolIn(configuredPairs)).thenReturn(existingPairs);

        ResponseEntity<String> mockResponse = ResponseEntity.ok("{\"symbols\":[]}");
        when(restTemplate.getForEntity(anyString(), eq(String.class))).thenReturn(mockResponse);

        try {
            when(objectMapper.readTree(anyString())).thenReturn(rootNode);

            PairInfo mockPairInfo = getPairInfo();

            when(objectMapper.readValue(anyString(), eq(PairInfo.class))).thenReturn(mockPairInfo);
        } catch (Exception e) {
            fail("Failed to setup mock behavior for ObjectMapper", e);
        }

        pairsService.filterMissingPairs();

        verify(cloudProperties).getPairs();
        verify(supportedPairRepository).findAllByPairSymbolIn(configuredPairs);
        verify(restTemplate, times(2)).getForEntity(anyString(), eq(String.class));
        verify(supportedPairRepository).saveAll(pairsCaptor.capture());

        List<SupportedPair> savedPairs = pairsCaptor.getValue();
        assertNotNull(savedPairs);
        assertFalse(savedPairs.isEmpty());
    }

    private static PairInfo getPairInfo() {
        PairInfo mockPairInfo = new PairInfo();
        List<SymbolInfo> symbols = new ArrayList<>();
        SymbolInfo symbolInfo = new SymbolInfo();
        symbolInfo.setSymbol("ETHUSDT");
        symbolInfo.setBaseAsset("ETH");
        symbolInfo.setQuoteAsset("USDT");

        List<FilterInfo> filters = new ArrayList<>();
        FilterInfo filterInfo = new FilterInfo();
        filterInfo.setFilterType("NOTIONAL");
        filterInfo.setMinNotional("10.00000000");
        filters.add(filterInfo);

        symbolInfo.setFilters(filters);
        symbols.add(symbolInfo);
        mockPairInfo.setSymbols(symbols);
        return mockPairInfo;
    }

    @Test
    void filterMissingPairs_WithNullConfiguredPairs_ShouldLogWarningAndReturn() {
        when(cloudProperties.getPairs()).thenReturn(null);

        pairsService.filterMissingPairs();

        verify(cloudProperties).getPairs();
        verifyNoInteractions(supportedPairRepository);
        verifyNoInteractions(restTemplate);
    }

    @Test
    void filterMissingPairs_WithEmptyConfiguredPairs_ShouldLogWarningAndReturn() {
        when(cloudProperties.getPairs()).thenReturn(Collections.emptyList());

        pairsService.filterMissingPairs();

        verify(cloudProperties).getPairs();
        verifyNoInteractions(supportedPairRepository);
        verifyNoInteractions(restTemplate);
    }

    @Test
    void filterMissingPairs_WithNoMissingPairs_ShouldNotFetchAnyPairs() {
        List<String> onlyExistingPairs = List.of("BTCUSDT");
        when(cloudProperties.getPairs()).thenReturn(onlyExistingPairs);
        when(supportedPairRepository.findAllByPairSymbolIn(onlyExistingPairs)).thenReturn(existingPairs);

        pairsService.filterMissingPairs();

        verify(cloudProperties).getPairs();
        verify(supportedPairRepository).findAllByPairSymbolIn(onlyExistingPairs);
        verifyNoInteractions(restTemplate);
    }

    @Test
    void filterMissingPairs_WhenApiCallFails_ShouldHandleException() {
        when(cloudProperties.getPairs()).thenReturn(configuredPairs);
        when(supportedPairRepository.findAllByPairSymbolIn(configuredPairs)).thenReturn(existingPairs);

        when(restTemplate.getForEntity(anyString(), eq(String.class)))
                .thenThrow(new RuntimeException("API connection error"));

        pairsService.filterMissingPairs();

        verify(cloudProperties).getPairs();
        verify(supportedPairRepository).findAllByPairSymbolIn(configuredPairs);
        verify(restTemplate, times(2)).getForEntity(anyString(), eq(String.class));
        verify(supportedPairRepository, never()).saveAll(anyList());
    }
}