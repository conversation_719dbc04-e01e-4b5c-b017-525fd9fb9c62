package org.example.cts.currencyservice.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.example.cts.currencyservice.config.CloudProperties;
import org.example.cts.currencyservice.dto.CryptoPriceEvent;
import org.example.cts.currencyservice.model.ExchangeRate;
import org.example.cts.currencyservice.model.enumType.GranularityPairs;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Collection;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CurrencyServiceTest {

    @Mock
    private CloudProperties cloudProperties;

    @Mock
    private PairsService pairsService;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private MongoTemplate mongoTemplate;

    @Captor
    private ArgumentCaptor<Collection<ExchangeRate>> exchangeRatesCaptor;

    @InjectMocks
    private CurrencyService currencyService;

    @Test
    void init_ShouldCallFilterMissingPairs() {
        when(cloudProperties.getPairs()).thenReturn(java.util.List.of("BTCUSDT", "ETHUSDT"));

        currencyService.init();

        verify(pairsService).filterMissingPairs();
        verify(cloudProperties).getPairs();
    }

    @Test
    void onRefresh_ShouldCallFilterMissingPairs() {
        mock(RefreshScopeRefreshedEvent.class);

        currencyService.onRefresh();

        verify(pairsService).filterMissingPairs();
    }

    @Test
    void handleNewPrice_ShouldAddValidEventToBuffer() throws Exception {
        String rawMessage = "{\"symbol\":\"BTCUSDT\",\"price\":\"45000.50\",\"timestamp\":1620000000.123}";
        CryptoPriceEvent event = new CryptoPriceEvent(
                "BTCUSDT",
                new BigDecimal("45000.50"),
                new BigDecimal("2323222.2221"),
                1620000000.123
        );
        when(objectMapper.readValue(rawMessage, CryptoPriceEvent.class)).thenReturn(event);

        currencyService.handleNewPrice(rawMessage);

        currencyService.flushBufferToMongo();
        verify(mongoTemplate).insert(exchangeRatesCaptor.capture(), eq("exchange_rates"));

        List<ExchangeRate> capturedRates = exchangeRatesCaptor.getValue().stream().toList();
        assertEquals(1, capturedRates.size());
        ExchangeRate rate = capturedRates.get(0);
        assertEquals("BTCUSDT", rate.getPairs());
        assertEquals(new BigDecimal("45000.50"), rate.getPrice());
        assertEquals(Instant.ofEpochMilli(1620000000123L), rate.getTimestamp());
        assertEquals(GranularityPairs.SECONDS_5, rate.getGranularity());
    }

    @Test
    void handleNewPrice_ShouldNotAddEventWithNullSymbol() throws Exception {
        String rawMessage = "{\"symbol\":null,\"price\":\"45000.50\",\"timestamp\":1620000000.123}";
        CryptoPriceEvent event = new CryptoPriceEvent(
                null,
                new BigDecimal("45000.50"),
                new BigDecimal("2323222.2221"),
                1620000000.123
        );
        when(objectMapper.readValue(rawMessage, CryptoPriceEvent.class)).thenReturn(event);

        currencyService.handleNewPrice(rawMessage);

        currencyService.flushBufferToMongo();
        verify(mongoTemplate, never()).insert(any(List.class), eq("exchange_rates"));
    }

    @Test
    void handleNewPrice_ShouldNotAddEventWithNullPrice() throws Exception {
        String rawMessage = "{\"symbol\":\"BTCUSDT\",\"price\":null,\"timestamp\":1620000000.123}";
        CryptoPriceEvent event = new CryptoPriceEvent(
                "BTCUSDT",
                null,
                new BigDecimal("2323222.2221"),
                1620000000.123
        );
        when(objectMapper.readValue(rawMessage, CryptoPriceEvent.class)).thenReturn(event);

        currencyService.handleNewPrice(rawMessage);

        currencyService.flushBufferToMongo();
        verify(mongoTemplate, never()).insert(any(List.class), eq("exchange_rates"));
    }

    @Test
    void handleNewPrice_ShouldNotAddEventWithNonPositivePrice() throws Exception {
        String rawMessage = "{\"symbol\":\"BTCUSDT\",\"price\":\"0\",\"timestamp\":1620000000.123}";
        CryptoPriceEvent event = new CryptoPriceEvent(
                "BTCUSDT",
                BigDecimal.ZERO,
                new BigDecimal("2323222.2221"),
                1620000000.123
        );
        when(objectMapper.readValue(rawMessage, CryptoPriceEvent.class)).thenReturn(event);

        currencyService.handleNewPrice(rawMessage);

        currencyService.flushBufferToMongo();
        verify(mongoTemplate, never()).insert(any(List.class), eq("exchange_rates"));
    }

    @Test
    void handleNewPrice_ShouldHandleParsingException() throws Exception {
        String invalidJson = "invalid json";
        when(objectMapper.readValue(invalidJson, CryptoPriceEvent.class))
                .thenThrow(new com.fasterxml.jackson.core.JsonParseException(null, "Bad JSON"));

        assertDoesNotThrow(() -> currencyService.handleNewPrice(invalidJson));

        currencyService.flushBufferToMongo();
        verify(mongoTemplate, never()).insert(any(List.class), eq("exchange_rates"));
    }

    @Test
    void flushBufferToMongo_ShouldNotDoAnythingWhenBufferIsEmpty() {
        currencyService.flushBufferToMongo();

        verify(mongoTemplate, never()).insert(any(List.class), eq("exchange_rates"));
    }

    @Test
    void flushBufferToMongo_ShouldHandleMongoException() throws Exception {
        String rawMessage = "{\"symbol\":\"BTCUSDT\",\"price\":\"45000.50\",\"timestamp\":1620000000.123}";
        CryptoPriceEvent event = new CryptoPriceEvent(
                "BTCUSDT",
                new BigDecimal("45000.50"),
                new BigDecimal("2323222.2221"),
                1620000000.123
        );
        when(objectMapper.readValue(rawMessage, CryptoPriceEvent.class)).thenReturn(event);

        doThrow(new RuntimeException("Database error"))
                .when(mongoTemplate).insert(any(List.class), eq("exchange_rates"));

        currencyService.handleNewPrice(rawMessage);
        currencyService.flushBufferToMongo();

        verify(mongoTemplate).insert(any(List.class), eq("exchange_rates"));

        currencyService.flushBufferToMongo();
        verify(mongoTemplate, times(1)).insert(any(List.class), eq("exchange_rates"));
    }

    @Test
    void flushBufferToMongo_ShouldConvertMultipleEventsCorrectly() throws Exception {
        String btcMessage = "{\"symbol\":\"BTCUSDT\",\"price\":\"45000.50\",\"timestamp\":1620000000.123}";
        String ethMessage = "{\"symbol\":\"ETHUSDT\",\"price\":\"3000.75\",\"timestamp\":1620000010.456}";

        CryptoPriceEvent btcEvent = new CryptoPriceEvent(
                "BTCUSDT",
                new BigDecimal("45000.50"),
                new BigDecimal("2323222.2221"),
                1620000000.123
        );
        CryptoPriceEvent ethEvent = new CryptoPriceEvent(
                "ETHUSDT",
                new BigDecimal("3000.75"),
                new BigDecimal("2323222.2221"),
                1620000010.456
        );

        when(objectMapper.readValue(btcMessage, CryptoPriceEvent.class)).thenReturn(btcEvent);
        when(objectMapper.readValue(ethMessage, CryptoPriceEvent.class)).thenReturn(ethEvent);

        currencyService.handleNewPrice(btcMessage);
        currencyService.handleNewPrice(ethMessage);
        currencyService.flushBufferToMongo();

        verify(mongoTemplate).insert(exchangeRatesCaptor.capture(), eq("exchange_rates"));

        List<ExchangeRate> capturedRates = exchangeRatesCaptor.getValue().stream().toList();
        assertEquals(2, capturedRates.size());

        assertTrue(capturedRates.stream().anyMatch(rate -> "BTCUSDT".equals(rate.getPairs())));
        assertTrue(capturedRates.stream().anyMatch(rate -> "ETHUSDT".equals(rate.getPairs())));
    }
}