package org.example.cts.currencyservice.repository;

import org.example.cts.currencyservice.model.SupportedPair;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SupportedPairRepositoryTest {

    @Mock
    private SupportedPairRepository supportedPairRepository;

    @Test
    void findAllByPairSymbolIn_ShouldReturnMatchingPairs() {
        List<String> pairSymbols = Arrays.asList("BTCUSDT", "ETHUSDT");

        SupportedPair btcPair = SupportedPair.builder()
                .id(UUID.randomUUID())
                .baseCurrencyCode("BTC")
                .targetCurrencyCode("USDT")
                .pairSymbol("BTCUSDT")
                .build();

        SupportedPair ethPair = SupportedPair.builder()
                .id(UUID.randomUUID())
                .baseCurrencyCode("ETH")
                .targetCurrencyCode("USDT")
                .pairSymbol("ETHUSDT")
                .build();

        List<SupportedPair> expectedPairs = Arrays.asList(btcPair, ethPair);

        when(supportedPairRepository.findAllByPairSymbolIn(pairSymbols)).thenReturn(expectedPairs);

        List<SupportedPair> result = supportedPairRepository.findAllByPairSymbolIn(pairSymbols);

        assertNotNull(result);
        assertEquals(2, result.size());
        verify(supportedPairRepository).findAllByPairSymbolIn(pairSymbols);
    }

    @Test
    void findByPairSymbol_WithExistingSymbol_ShouldReturnPair() {
        String pairSymbol = "BTCUSDT";

        SupportedPair btcPair = SupportedPair.builder()
                .id(UUID.randomUUID())
                .baseCurrencyCode("BTC")
                .targetCurrencyCode("USDT")
                .pairSymbol(pairSymbol)
                .minOrderAmount(new BigDecimal("10.0"))
                .feePercentage(new BigDecimal("0.1"))
                .build();

        when(supportedPairRepository.findByPairSymbol(pairSymbol)).thenReturn(Optional.of(btcPair));

        Optional<SupportedPair> result = supportedPairRepository.findByPairSymbol(pairSymbol);

        assertTrue(result.isPresent());
        assertEquals(pairSymbol, result.get().getPairSymbol());
        verify(supportedPairRepository).findByPairSymbol(pairSymbol);
    }

    @Test
    void findByPairSymbol_WithNonExistingSymbol_ShouldReturnEmptyOptional() {
        String pairSymbol = "NONEXISTENT";

        when(supportedPairRepository.findByPairSymbol(pairSymbol)).thenReturn(Optional.empty());

        Optional<SupportedPair> result = supportedPairRepository.findByPairSymbol(pairSymbol);

        assertFalse(result.isPresent());
        verify(supportedPairRepository).findByPairSymbol(pairSymbol);
    }

    @Test
    void findById_WithExistingId_ShouldReturnPair() {
        UUID id = UUID.randomUUID();

        SupportedPair pair = SupportedPair.builder()
                .id(id)
                .baseCurrencyCode("BTC")
                .targetCurrencyCode("USDT")
                .pairSymbol("BTCUSDT")
                .build();

        when(supportedPairRepository.findById(id)).thenReturn(Optional.of(pair));

        Optional<SupportedPair> result = supportedPairRepository.findById(id);

        assertTrue(result.isPresent());
        assertEquals(id, result.get().getId());
        verify(supportedPairRepository).findById(id);
    }

    @Test
    void saveAll_ShouldPersistAllPairs() {
        SupportedPair pair1 = SupportedPair.builder()
                .id(UUID.randomUUID())
                .baseCurrencyCode("BTC")
                .targetCurrencyCode("USDT")
                .pairSymbol("BTCUSDT")
                .build();

        SupportedPair pair2 = SupportedPair.builder()
                .id(UUID.randomUUID())
                .baseCurrencyCode("ETH")
                .targetCurrencyCode("USDT")
                .pairSymbol("ETHUSDT")
                .build();

        List<SupportedPair> pairs = Arrays.asList(pair1, pair2);

        when(supportedPairRepository.saveAll(pairs)).thenReturn(pairs);

        List<SupportedPair> result = supportedPairRepository.saveAll(pairs);

        assertNotNull(result);
        assertEquals(2, result.size());
        verify(supportedPairRepository).saveAll(pairs);
    }

    @Test
    void count_ShouldReturnTotalNumberOfPairs() {
        when(supportedPairRepository.count()).thenReturn(5L);

        long result = supportedPairRepository.count();

        assertEquals(5L, result);
        verify(supportedPairRepository).count();
    }
}