package org.example.cts.currencyservice.model;

import org.example.cts.currencyservice.model.enumType.GranularityPairs;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;
import java.time.Instant;
import static org.junit.jupiter.api.Assertions.*;

class ExchangeRateTest {

    @Test
    void exchangeRate_shouldHaveCorrectBuilder() {
        Instant now = Instant.now();
        BigDecimal price = new BigDecimal("1.2345");

        ExchangeRate rate = ExchangeRate.builder()
                .timestamp(now)
                .pairs("BTCUSD")
                .price(price)
                .granularity(GranularityPairs.MINUTES_1)
                .build();

        assertEquals(now, rate.getTimestamp());
        assertEquals("BTCUSD", rate.getPairs());
        assertEquals(price, rate.getPrice());
        assertEquals(GranularityPairs.MINUTES_1, rate.getGranularity());
    }
}