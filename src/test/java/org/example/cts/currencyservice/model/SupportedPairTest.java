package org.example.cts.currencyservice.model;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class SupportedPairTest {

    @Test
    void builder_ShouldCreateSupportedPairWithAllProperties() {
        UUID id = UUID.randomUUID();
        String baseCurrencyCode = "BTC";
        String targetCurrencyCode = "USDT";
        String pairSymbol = "BTCUSDT";
        BigDecimal minOrderAmount = new BigDecimal("10.0");
        BigDecimal feePercentage = new BigDecimal("0.1");

        SupportedPair pair = SupportedPair.builder()
                .id(id)
                .baseCurrencyCode(baseCurrencyCode)
                .targetCurrencyCode(targetCurrencyCode)
                .pairSymbol(pairSymbol)
                .minOrderAmount(minOrderAmount)
                .feePercentage(feePercentage)
                .build();

        assertNotNull(pair);
        assertEquals(id, pair.getId());
        assertEquals(baseCurrencyCode, pair.getBaseCurrencyCode());
        assertEquals(targetCurrencyCode, pair.getTargetCurrencyCode());
        assertEquals(pairSymbol, pair.getPairSymbol());
        assertEquals(0, minOrderAmount.compareTo(pair.getMinOrderAmount()));
        assertEquals(0, feePercentage.compareTo(pair.getFeePercentage()));
    }

    @Test
    void settersAndGetters_ShouldWorkCorrectly() {
        UUID id = UUID.randomUUID();
        SupportedPair pair = new SupportedPair(
                id,
                "ETH",
                "USDT",
                "ETHUSDT",
                new BigDecimal("5.0"),
                new BigDecimal("0.2"),
                8,
                8
        );

        assertEquals(id, pair.getId());
        assertEquals("ETH", pair.getBaseCurrencyCode());
        assertEquals("USDT", pair.getTargetCurrencyCode());
        assertEquals("ETHUSDT", pair.getPairSymbol());
        assertEquals(0, new BigDecimal("5.0").compareTo(pair.getMinOrderAmount()));
        assertEquals(0, new BigDecimal("0.2").compareTo(pair.getFeePercentage()));
    }
}