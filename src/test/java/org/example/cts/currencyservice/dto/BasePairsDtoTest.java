package org.example.cts.currencyservice.dto;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class BasePairsDtoTest {

    @Test
    void basePairsDto_shouldHaveCorrectFieldsAndBuilder() {
        BasePairsDto dto = BasePairsDto.builder()
                .id("1")
                .baseCurrencyCode("USD")
                .targetCurrencyCode("EUR")
                .pairSymbol("USDEUR")
                .build();

        assertEquals("1", dto.getId());
        assertEquals("USD", dto.getBaseCurrencyCode());
        assertEquals("EUR", dto.getTargetCurrencyCode());
        assertEquals("USDEUR", dto.getPairSymbol());
    }

    @Test
    void basePairsDto_shouldHaveNoArgsConstructor() {
        BasePairsDto dto = new BasePairsDto();
        dto.setId("2");
        dto.setBaseCurrencyCode("GBP");
        dto.setTargetCurrencyCode("JPY");
        dto.setPairSymbol("GBPJPY");

        assertEquals("2", dto.getId());
        assertEquals("GBP", dto.getBaseCurrencyCode());
        assertEquals("JPY", dto.getTargetCurrencyCode());
        assertEquals("GBPJPY", dto.getPairSymbol());
    }
}