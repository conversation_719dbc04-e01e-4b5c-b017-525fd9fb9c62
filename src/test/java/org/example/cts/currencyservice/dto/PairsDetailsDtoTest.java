package org.example.cts.currencyservice.dto;

import org.example.cts.currencyservice.model.SupportedPair;
import org.junit.jupiter.api.Test;
import java.math.BigDecimal;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class PairsDetailsDtoTest {

    @Test
    void pairsDetailsDto_shouldMapFromSupportedPairCorrectly() {
        UUID pairId = UUID.randomUUID();
        SupportedPair pair = new SupportedPair(
                pairId,
                "ETH",
                "BTC",
                "ETHBTC",
                new BigDecimal("0.01"),
                new BigDecimal("0.1"),
                8,
                8
        );

        PairsDetailsDto dto = PairsDetailsDto.mapToDto(pair);

        assertEquals("ETH", dto.getBaseCode());
        assertEquals("BTC", dto.getTargetCode());
        assertEquals("ETHBTC", dto.getPairSymbol());
        assertEquals(new BigDecimal("0.01"), dto.getMinOrderAmount());
        assertEquals(new BigDecimal("0.1"), dto.getFeePercentage());
    }

    @Test
    void pairsDetailsDto_shouldHaveAllArgsConstructor() {
        PairsDetailsDto dto = new PairsDetailsDto(
                "XRP",
                "USD",
                "XRPUSD",
                new BigDecimal("10"),
                new BigDecimal("0.2"),
                3,
                3
        );

        assertEquals("XRP", dto.getBaseCode());
        assertEquals("USD", dto.getTargetCode());
        assertEquals("XRPUSD", dto.getPairSymbol());
        assertEquals(new BigDecimal("10"), dto.getMinOrderAmount());
        assertEquals(new BigDecimal("0.2"), dto.getFeePercentage());
    }
}