package org.example.cts.currencyservice.dto;

import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class PaginatedResponseTest {

    @Test
    void from_ShouldCorrectlyMapFromPage() {
        List<String> items = Arrays.asList("item1", "item2", "item3");
        Page<String> page = new PageImpl<>(items, PageRequest.of(0, 10), 3);

        PaginatedResponse<String> result = PaginatedResponse.from(page);

        assertNotNull(result);
        assertEquals(3, result.getTotalElements());
        assertEquals(1, result.getTotalPages());
        assertEquals(0, result.getPageable().getPageNumber());
        assertEquals(10, result.getSize());
        assertEquals(3, result.getContent().size());
        assertEquals("item1", result.getContent().get(0));
        assertEquals("item2", result.getContent().get(1));
        assertEquals("item3", result.getContent().get(2));
    }

    @Test
    void from_WithEmptyPage_ShouldReturnEmptyContent() {
        Page<String> emptyPage = new PageImpl<>(List.of(), PageRequest.of(0, 10), 0);

        PaginatedResponse<String> result = PaginatedResponse.from(emptyPage);

        assertNotNull(result);
        assertEquals(0, result.getTotalElements());
        assertEquals(0, result.getTotalPages());
        assertTrue(result.getContent().isEmpty());
    }
}