package org.example.cts.currencyservice.dto;

import org.junit.jupiter.api.Test;
import java.util.List;
import static org.junit.jupiter.api.Assertions.*;

class SymbolInfoTest {

    @Test
    void symbolInfo_shouldHaveCorrectFields() {
        FilterInfo filterInfo = new FilterInfo();
        filterInfo.setFilterType("PRICE_FILTER");

        SymbolInfo symbolInfo = new SymbolInfo();
        symbolInfo.setSymbol("LTCBTC");
        symbolInfo.setBaseAsset("LTC");
        symbolInfo.setQuoteAsset("BTC");
        symbolInfo.setFilters(List.of(filterInfo));

        assertEquals("LTCBTC", symbolInfo.getSymbol());
        assertEquals("LTC", symbolInfo.getBaseAsset());
        assertEquals("BTC", symbolInfo.getQuoteAsset());
        assertEquals(1, symbolInfo.getFilters().size());
        assertEquals("PRICE_FILTER", symbolInfo.getFilters().get(0).getFilterType());
    }
}