package org.example.cts.currencyservice.dto;

import org.junit.jupiter.api.Test;
import java.util.List;
import static org.junit.jupiter.api.Assertions.*;

class PairInfoTest {

    @Test
    void pairInfo_shouldHaveCorrectFields() {
        SymbolInfo symbolInfo = new SymbolInfo();
        symbolInfo.setSymbol("BTCUSDT");

        PairInfo pairInfo = new PairInfo();
        pairInfo.setSymbols(List.of(symbolInfo));

        assertEquals(1, pairInfo.getSymbols().size());
        assertEquals("BTCUSDT", pairInfo.getSymbols().get(0).getSymbol());
    }
}