package org.example.cts.currencyservice.dto;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class FilterInfoTest {

    @Test
    void filterInfo_shouldHaveCorrectFields() {
        FilterInfo filterInfo = new FilterInfo();
        filterInfo.setFilterType("LOT_SIZE");
        filterInfo.setMinNotional("10.0");
        filterInfo.setMinQty("1.0");

        assertEquals("LOT_SIZE", filterInfo.getFilterType());
        assertEquals("10.0", filterInfo.getMinNotional());
        assertEquals("1.0", filterInfo.getMinQty());
    }
}