package org.example.cts.currencyservice;

import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.boot.SpringApplication;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

class CurrencyServiceApplicationTests {

    @Test
    void mainMethodShouldStartApplication() {
        try (MockedStatic<SpringApplication> mockedSpringApplication = Mockito.mockStatic(SpringApplication.class)) {
            String[] args = new String[0];

            assertDoesNotThrow(() -> CurrencyServiceApplication.main(args), "Main method should not throw exceptions");

            mockedSpringApplication.verify(() ->
                            SpringApplication.run(CurrencyServiceApplication.class, args),
                    Mockito.times(1));
        }
    }


}
