package org.example.cts.currencyservice.config;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoDatabase;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.MongoTemplate;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MongoConfigTest {

    @Mock
    private MongoClient mongoClient;

    @Mock
    private MongoTemplate mongoTemplate;

    @Mock
    private MongoDatabase mongoDatabase;

    @InjectMocks
    private MongoConfig mongoConfig;

    @Test
    void initializeTimeSeriesCollection_shouldCreateCollection_whenNotExists() {
        mongoConfig.databaseName = "testDB";
        when(mongoClient.getDatabase(anyString())).thenReturn(mongoDatabase);
        when(mongoTemplate.collectionExists(anyString())).thenReturn(false);

        mongoConfig.initializeTimeSeriesCollection();

        verify(mongoDatabase).createCollection(eq("exchange_rates"), any());
    }

    @Test
    void initializeTimeSeriesCollection_shouldNotCreateCollection_whenExists() {
        mongoConfig.databaseName = "testDB";
        when(mongoClient.getDatabase(anyString())).thenReturn(mongoDatabase);
        when(mongoTemplate.collectionExists(anyString())).thenReturn(true);

        mongoConfig.initializeTimeSeriesCollection();

        verify(mongoDatabase, never()).createCollection(anyString(), any());
    }
}