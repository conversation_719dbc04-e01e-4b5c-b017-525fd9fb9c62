package org.example.cts.currencyservice.config;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.junit.jupiter.api.Test;
import org.springframework.kafka.core.ConsumerFactory;

import static org.junit.jupiter.api.Assertions.*;

class KafkaConfigTest {

    @Test
    void consumerFactory_shouldReturnValidFactory() {
        KafkaConfig kafkaConfig = new KafkaConfig();
        kafkaConfig.bootstrapServers = "localhost:9092";

        ConsumerFactory<String, String> factory = kafkaConfig.consumerFactory();

        assertNotNull(factory);
        assertEquals("localhost:9092", factory.getConfigurationProperties().get(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG));
        assertEquals("earliest", factory.getConfigurationProperties().get(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG));
    }

    @Test
    void consumerConfig_shouldContainRequiredProperties() {
        KafkaConfig kafkaConfig = new KafkaConfig();
        kafkaConfig.bootstrapServers = "localhost:9092";

        var config = kafkaConfig.consumerConfig();

        assertEquals("localhost:9092", config.get(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG));
        assertEquals("earliest", config.get(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG));
    }
}