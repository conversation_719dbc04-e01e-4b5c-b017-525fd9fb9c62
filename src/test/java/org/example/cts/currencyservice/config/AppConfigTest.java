package org.example.cts.currencyservice.config;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class AppConfigTest {

    @Test
    void restTemplate_ShouldReturnRestTemplateInstance() {
        AppConfig appConfig = new AppConfig();

        RestTemplate result = appConfig.restTemplate();

        assertNotNull(result);
    }

    @Test
    void baseUrl_ShouldBeDefinedCorrectly() {
        assertNotNull(AppConfig.BASE_URL);
        assertEquals("/v1", AppConfig.BASE_URL);
    }
}