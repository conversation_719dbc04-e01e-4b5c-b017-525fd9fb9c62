package org.example.cts.currencyservice.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class CloudPropertiesTest {

    @InjectMocks
    private CloudProperties cloudProperties;

    @BeforeEach
    void setUp() {
        cloudProperties = new CloudProperties();
    }

    @Test
    void getPairs_WhenPairsSet_ShouldReturnCorrectValues() {
        List<String> expectedPairs = Arrays.asList("BTCUSDT", "ETHUSDT", "ADAUSDT");
        cloudProperties.setPairs(expectedPairs);

        List<String> actualPairs = cloudProperties.getPairs();

        assertNotNull(actualPairs);
        assertEquals(expectedPairs.size(), actualPairs.size());
        assertTrue(actualPairs.containsAll(expectedPairs));
    }

    @Test
    void setPairs_ShouldCorrectlyUpdatePairs() {
        List<String> initialPairs = Arrays.asList("BTCUSDT", "ETHUSDT");
        cloudProperties.setPairs(initialPairs);

        List<String> newPairs = Arrays.asList("BTCUSDT", "ADAUSDT", "DOGEUSDT");

        cloudProperties.setPairs(newPairs);
        List<String> actualPairs = cloudProperties.getPairs();

        assertNotNull(actualPairs);
        assertEquals(newPairs.size(), actualPairs.size());
        assertTrue(actualPairs.containsAll(newPairs));
    }

    @Test
    void defaultConstructor_ShouldInitializePairsAsNull() {
        CloudProperties newProperties = new CloudProperties();

        assertNull(newProperties.getPairs());
    }
}