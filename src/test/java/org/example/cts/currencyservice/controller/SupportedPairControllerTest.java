package org.example.cts.currencyservice.controller;

import org.example.cts.currencyservice.dto.BasePairsDto;
import org.example.cts.currencyservice.dto.PaginatedResponse;
import org.example.cts.currencyservice.dto.PairsDetailsDto;
import org.example.cts.currencyservice.service.SupportedPairService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SupportedPairControllerTest {

    @Mock
    private SupportedPairService supportedPairService;

    @InjectMocks
    private SupportedPairController supportedPairController;

    private PairsDetailsDto pairDetailsDto;
    private UUID pairId;
    private Page<BasePairsDto> pairsPage;

    @BeforeEach
    void setUp() {
        pairId = UUID.randomUUID();

        pairDetailsDto = new PairsDetailsDto();
        pairDetailsDto.setPairSymbol("BTCUSDT");
        pairDetailsDto.setBaseCode("BTC");
        pairDetailsDto.setTargetCode("USDT");
        pairDetailsDto.setMinOrderAmount(new BigDecimal("10.0"));
        pairDetailsDto.setFeePercentage(new BigDecimal("0.1"));

        BasePairsDto basePairsDto = new BasePairsDto(
                pairId.toString(),
                "BTC",
                "USDT",
                "BTCUSDT"
        );

        pairsPage = new PageImpl<>(List.of(basePairsDto), PageRequest.of(0, 10), 1);
    }

    @Test
    void getPairs_ShouldReturnPaginatedResponse() {
        when(supportedPairService.getPairs(anyInt(), anyInt(), anyString(), anyString(), anyString()))
                .thenReturn(pairsPage);

        ResponseEntity<PaginatedResponse<BasePairsDto>> response = supportedPairController.getPairs(
                0, 10, "pairSymbol", "asc", "");

        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().getTotalElements());
        assertEquals(1, response.getBody().getContent().size());
        assertEquals("BTCUSDT", response.getBody().getContent().get(0).getPairSymbol());

        verify(supportedPairService).getPairs(0, 10, "pairSymbol", "asc", "");
    }

    @Test
    void getPairById_WhenPairExists_ShouldReturnPairDetails() {
        when(supportedPairService.getPairById(pairId)).thenReturn(pairDetailsDto);

        ResponseEntity<?> response = supportedPairController.getPairById(pairId);

        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertInstanceOf(PairsDetailsDto.class, response.getBody());

        PairsDetailsDto resultDto = (PairsDetailsDto) response.getBody();
        assertEquals("BTCUSDT", resultDto.getPairSymbol());

        verify(supportedPairService).getPairById(pairId);
    }

    @Test
    void getPairById_WhenPairDoesNotExist_ShouldPropagateException() {
        when(supportedPairService.getPairById(pairId))
                .thenThrow(new ResponseStatusException(HttpStatus.NOT_FOUND, "Pair not found"));

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () ->
                supportedPairController.getPairById(pairId)
        );

        assertEquals("404 NOT_FOUND \"Pair not found\"", exception.getMessage());
        verify(supportedPairService).getPairById(pairId);
    }

    @Test
    void getPairByPairSymbol_WhenPairExists_ShouldReturnPairDetails() {
        String pairSymbol = "BTCUSDT";
        when(supportedPairService.getPairByPairSymbol(pairSymbol)).thenReturn(pairDetailsDto);

        ResponseEntity<?> response = supportedPairController.getPairByPairSymbol(pairSymbol);

        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertInstanceOf(PairsDetailsDto.class, response.getBody());

        PairsDetailsDto resultDto = (PairsDetailsDto) response.getBody();
        assertEquals("BTCUSDT", resultDto.getPairSymbol());

        verify(supportedPairService).getPairByPairSymbol(pairSymbol);
    }

    @Test
    void getPairByPairSymbol_WhenPairDoesNotExist_ShouldPropagateException() {
        String pairSymbol = "BTCUSDT";
        when(supportedPairService.getPairByPairSymbol(pairSymbol))
                .thenThrow(new ResponseStatusException(HttpStatus.NOT_FOUND, "Pair not found"));

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () ->
                supportedPairController.getPairByPairSymbol(pairSymbol)
        );

        assertEquals("404 NOT_FOUND \"Pair not found\"", exception.getMessage());
        verify(supportedPairService).getPairByPairSymbol(pairSymbol);
    }
}