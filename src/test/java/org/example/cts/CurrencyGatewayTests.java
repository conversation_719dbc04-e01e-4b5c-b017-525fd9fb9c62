package org.example.cts;

import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.boot.SpringApplication;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import org.springframework.test.context.ActiveProfiles;

class CurrencyGatewayTests {

	@Test
	void mainMethodShouldStartApplication() {
		try (MockedStatic<SpringApplication> mockedSpringApplication = Mockito.mockStatic(SpringApplication.class)) {
			String[] args = new String[0];

			assertDoesNotThrow(() -> CurrencyGateway.main(args), "Main method should not throw exceptions");

			mockedSpringApplication.verify(() ->
				SpringApplication.run(CurrencyGateway.class, args),
				Mockito.times(1));
		}
	}
}
