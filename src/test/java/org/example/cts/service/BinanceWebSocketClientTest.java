package org.example.cts.service;

import org.java_websocket.handshake.ServerHandshake;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.net.URI;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BinanceWebSocketClientTest {

    @Mock
    private BinanceService binanceService;

    @Mock
    private ServerHandshake serverHandshake;

    @Mock
    private ScheduledExecutorService executorService;

    private BinanceWebSocketClient client;
    private URI uri;

    @BeforeEach
    void setUp() throws Exception {
        uri = new URI("wss://stream.binance.com:9443/stream?streams=");
        client = spy(new BinanceWebSocketClient(uri, binanceService, executorService));
    }

    @Test
    void shouldInitializeWithCorrectParameters() {
        assertEquals(uri, client.getURI());
        assertFalse(getReconnectingFlag(client));
        assertFalse(getIsClosedFlag(client));
        assertEquals(0, getReconnectAttempts(client));
        assertEquals(1000, getInitialReconnectDelay(client));
        assertEquals(60000, getMaxReconnectDelay(client));
        assertEquals(1000, getCurrentReconnectDelay(client));
    }

    @Test
    void shouldResetCountersOnSuccessfulConnection() {
        setReconnectAttempts(client, 5);
        setCurrentReconnectDelay(client);

        client.onOpen(serverHandshake);

        assertEquals(0, getReconnectAttempts(client));
        assertEquals(1000, getCurrentReconnectDelay(client));
    }

    @Test
    void shouldProcessMessages() {
        String message = "testMessage";
        client.onMessage(message);
        verify(binanceService).processMessage(message);
    }

    @Test
    void shouldHandleMessageProcessingErrors() {
        String message = "testMessage";
        doThrow(new RuntimeException("Processing error")).when(binanceService).processMessage(message);

        assertDoesNotThrow(() -> client.onMessage(message));
        verify(binanceService).processMessage(message);
    }

    @Test
    void shouldNotReconnectIfClosed() {
        setIsClosed(client);
        client.onClose(1000, "Normal closure", true);
        verify(executorService, never()).schedule(any(Runnable.class), anyLong(), any(TimeUnit.class));
        assertFalse(getReconnectingFlag(client));
    }

    @Test
    void shouldNotReconnectOnErrorIfAlreadyReconnecting() {
        setReconnecting(client);
        client.onError(new Exception("Test error"));
        verify(executorService, never()).schedule(any(Runnable.class), anyLong(), any(TimeUnit.class));
    }

    @Test
    void shouldNotReconnectOnErrorIfAlreadyOpen() {
        doReturn(true).when(client).isOpen();
        client.onError(new Exception("Test error"));
        verify(executorService, never()).schedule(any(Runnable.class), anyLong(), any(TimeUnit.class));
        assertFalse(getReconnectingFlag(client));
    }

    @Test
    void shouldGiveUpAfterMaximumReconnectAttempts() throws Exception {
        setReconnectAttempts(client, 10);

        Method attemptReconnect = BinanceWebSocketClient.class.getDeclaredMethod("attemptReconnect");
        attemptReconnect.setAccessible(true);
        attemptReconnect.invoke(client);

        verify(executorService, never()).schedule(any(Runnable.class), anyLong(), any(TimeUnit.class));
        assertFalse(getReconnectingFlag(client));
    }

    @Test
    void shouldCleanUpResourcesOnClose() {
        client.close();
        assertTrue(getIsClosedFlag(client));
        verify(executorService).shutdownNow();
    }

    @Test
    void shouldAttemptReconnectWithExponentialBackoff() throws Exception {
        setReconnectAttempts(client, 1);
        setCurrentReconnectDelay(client, 1000);
        Method attemptReconnect = BinanceWebSocketClient.class.getDeclaredMethod("attemptReconnect");
        attemptReconnect.setAccessible(true);

        attemptReconnect.invoke(client);

        verify(executorService).schedule(any(Runnable.class), anyLong(), eq(TimeUnit.MILLISECONDS));
        assertTrue(getReconnectingFlag(client));
    }

    @Test
    void shouldHandleReconnectSuccess() throws Exception {
        doNothing().when(client).reconnect();
        doReturn(false).when(client).isOpen();
        Method attemptReconnect = BinanceWebSocketClient.class.getDeclaredMethod("attemptReconnect");
        attemptReconnect.setAccessible(true);
        attemptReconnect.invoke(client);

        ArgumentCaptor<Runnable> runnableCaptor = ArgumentCaptor.forClass(Runnable.class);
        verify(executorService).schedule(runnableCaptor.capture(), anyLong(), eq(TimeUnit.MILLISECONDS));

        runnableCaptor.getValue().run();

        assertFalse(getReconnectingFlag(client));
        assertEquals(1000, getCurrentReconnectDelay(client));
    }

    @Test
    void shouldNotReconnectIfAlreadyOpen() throws Exception {
        Method attemptReconnect = BinanceWebSocketClient.class.getDeclaredMethod("attemptReconnect");
        attemptReconnect.setAccessible(true);
        attemptReconnect.invoke(client);

        ArgumentCaptor<Runnable> runnableCaptor = ArgumentCaptor.forClass(Runnable.class);
        verify(executorService).schedule(runnableCaptor.capture(), anyLong(), eq(TimeUnit.MILLISECONDS));

        doReturn(true).when(client).isOpen();
        runnableCaptor.getValue().run();

        assertFalse(getReconnectingFlag(client));
        verify(client, never()).reconnect();
    }

    @Test
    void shouldHandleReconnectFailureAndRetry() throws Exception {
        setReconnectAttempts(client, 1);
        setCurrentReconnectDelay(client, 1000);
        doReturn(false).when(client).isOpen();

        doAnswer(invocation -> {
            throw new RuntimeException("Connection failed");
        }).when(client).reconnect();

        Method attemptReconnect = BinanceWebSocketClient.class.getDeclaredMethod("attemptReconnect");
        attemptReconnect.setAccessible(true);
        attemptReconnect.invoke(client);

        ArgumentCaptor<Runnable> runnableCaptor = ArgumentCaptor.forClass(Runnable.class);
        verify(executorService).schedule(runnableCaptor.capture(), anyLong(), eq(TimeUnit.MILLISECONDS));

        Runnable runnable = runnableCaptor.getValue();
        runnable.run();

        assertFalse(getReconnectingFlag(client));
        assertTrue(getCurrentReconnectDelay(client) > 1000);
    }

    @Test
    void shouldAddJitterToReconnectDelay() throws Exception {
        setReconnectAttempts(client, 1);
        setCurrentReconnectDelay(client, 1000);

        Method attemptReconnect = BinanceWebSocketClient.class.getDeclaredMethod("attemptReconnect");
        attemptReconnect.setAccessible(true);

        attemptReconnect.invoke(client);

        ArgumentCaptor<Long> delayCaptor = ArgumentCaptor.forClass(Long.class);
        verify(executorService).schedule(any(Runnable.class), delayCaptor.capture(), eq(TimeUnit.MILLISECONDS));

        long delay = delayCaptor.getValue();
        assertTrue(delay >= 1000 && delay <= 1100);
    }

    @Test
    void shouldNotScheduleNewReconnectIfAlreadyInProgress() {
        setReconnecting(client);
        client.onClose(1000, "Test", true);

        verify(executorService, never()).schedule(any(Runnable.class), anyLong(), any(TimeUnit.class));
    }

    private void setCurrentReconnectDelay(BinanceWebSocketClient client, int value) {
        ReflectionTestUtils.setField(client, "currentReconnectDelay", value);
    }

    private boolean getReconnectingFlag(BinanceWebSocketClient client) {
        return getPrivateField(client, "reconnecting").get();
    }

    private void setReconnecting(BinanceWebSocketClient client) {
        getPrivateField(client, "reconnecting").set(true);
    }

    private boolean getIsClosedFlag(BinanceWebSocketClient client) {
        return getPrivateField(client, "isClosed").get();
    }

    private void setIsClosed(BinanceWebSocketClient client) {
        getPrivateField(client, "isClosed").set(true);
    }

    private int getReconnectAttempts(BinanceWebSocketClient client) {
        return (int) ReflectionTestUtils.getField(client, "reconnectAttempts");
    }

    private void setReconnectAttempts(BinanceWebSocketClient client, int value) {
        ReflectionTestUtils.setField(client, "reconnectAttempts", value);
    }

    private int getInitialReconnectDelay(BinanceWebSocketClient client) {
        return (int) ReflectionTestUtils.getField(client, "initialReconnectDelay");
    }

    private int getMaxReconnectDelay(BinanceWebSocketClient client) {
        return (int) ReflectionTestUtils.getField(client, "maxReconnectDelay");
    }

    private int getCurrentReconnectDelay(BinanceWebSocketClient client) {
        return (int) ReflectionTestUtils.getField(client, "currentReconnectDelay");
    }

    private void setCurrentReconnectDelay(BinanceWebSocketClient client) {
        ReflectionTestUtils.setField(client, "currentReconnectDelay", 16000);
    }

    private AtomicBoolean getPrivateField(BinanceWebSocketClient client, String fieldName) {
        try {
            Field field = BinanceWebSocketClient.class.getDeclaredField(fieldName);
            field.setAccessible(true);
            return (AtomicBoolean) field.get(client);
        } catch (Exception e) {
            throw new RuntimeException("Could not access field: " + fieldName, e);
        }
    }
}