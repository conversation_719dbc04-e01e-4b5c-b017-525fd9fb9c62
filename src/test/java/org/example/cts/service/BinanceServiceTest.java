package org.example.cts.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.example.cts.config.GatewayConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class BinanceServiceTest {

    @Mock
    private GatewayConfig gatewayConfig;

    @Mock
    private KafkaProducerService kafkaProducerService;

    @Spy
    private ObjectMapper objectMapper = new ObjectMapper();

    @InjectMocks
    private BinanceService binanceService;

    @BeforeEach
    void setUp() {
        List<String> defaultPairs = Arrays.asList("BTCUSDT", "ETHUSDT");
        when(gatewayConfig.getPairs()).thenReturn(defaultPairs);
        when(gatewayConfig.getKafkaTopicSendPriceUpdate()).thenReturn("crypto.prices.update");
        objectMapper.registerModule(new JavaTimeModule());

        ReflectionTestUtils.setField(binanceService, "currentPairs", new HashSet<>());
        ReflectionTestUtils.setField(binanceService, "streamToClientMap", new ConcurrentHashMap<>());
        ReflectionTestUtils.setField(binanceService, "activeConnections", ConcurrentHashMap.newKeySet());
        ReflectionTestUtils.setField(binanceService, "maxPairsPerConnection", 200);
        ReflectionTestUtils.setField(binanceService, "clientConnectionLostTimeout", 30);
        ReflectionTestUtils.setField(binanceService, "binanceBaseUrl", "wss://stream.binance.com:9443/stream?streams=");

    }

    @Test
    void shouldInitializeWithConfiguredPairs() {
        binanceService.init();

        Set<String> currentPairs = getCurrentPairsSet();
        Map<String, BinanceWebSocketClient> streamToClientMap = getStreamToClientMap();

        assertEquals(2, currentPairs.size());
        assertTrue(currentPairs.contains("BTCUSDT"));
        assertTrue(currentPairs.contains("ETHUSDT"));
        assertEquals(2, streamToClientMap.size());
        assertTrue(streamToClientMap.containsKey("BTCUSDT"));
        assertTrue(streamToClientMap.containsKey("ETHUSDT"));
    }

    @Test
    void shouldProcessMessageAndSendToKafkaImmediately() throws Exception {
        String symbol = "BTCUSDT";
        BigDecimal price = new BigDecimal("25000.50");
        BigDecimal volume = new BigDecimal("1.234");
        long timestamp = 1621234567890L;

        ObjectNode dataNode = objectMapper.createObjectNode()
                .put("s", symbol)
                .put("c", price.toString())
                .put("v", volume.toString())
                .put("E", timestamp);

        ObjectNode rootNode = objectMapper.createObjectNode();
        rootNode.set("data", dataNode);

        String message = objectMapper.writeValueAsString(rootNode);

        binanceService.processMessage(message);

        ArgumentCaptor<String> topicCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> messageCaptor = ArgumentCaptor.forClass(String.class);

        verify(kafkaProducerService).sendCryptoPriceEvent(topicCaptor.capture(), messageCaptor.capture());

        assertEquals("crypto.prices.update", topicCaptor.getValue());
        assertTrue(messageCaptor.getValue().contains(symbol));
        assertTrue(messageCaptor.getValue().contains(price.toString()));
    }

    @Test
    void shouldIgnoreIncompleteMessages() throws Exception {
        ObjectNode dataNode = objectMapper.createObjectNode()
                .put("s", "BTCUSDT")
                .put("E", 1621234567890L);

        ObjectNode rootNode = objectMapper.createObjectNode();
        rootNode.set("data", dataNode);

        String message = objectMapper.writeValueAsString(rootNode);

        binanceService.processMessage(message);

        verify(kafkaProducerService, never()).sendCryptoPriceEvent(anyString(), anyString());
    }

    @Test
    void shouldIgnoreMessagesWithMissingData() throws Exception {
        ObjectNode rootNode = objectMapper.createObjectNode();
        String message = objectMapper.writeValueAsString(rootNode);

        binanceService.processMessage(message);

        verify(kafkaProducerService, never()).sendCryptoPriceEvent(anyString(), anyString());
    }

    @Test
    void shouldHandleInvalidMessageFormat() {
        String message = "This is not a valid JSON message";

        binanceService.processMessage(message);

        verify(kafkaProducerService, never()).sendCryptoPriceEvent(anyString(), anyString());
    }

    @Test
    void shouldIgnoreRefreshWhenPairsUnchanged() {
        binanceService.init();

        BinanceService spyService = spy(binanceService);

        spyService.onRefresh();

        verify(spyService, never()).refreshPairs();
    }

    @Test
    void shouldDisconnectAndCleanupOnDestroy() {
        BinanceWebSocketClient mockClient1 = mock(BinanceWebSocketClient.class);
        BinanceWebSocketClient mockClient2 = mock(BinanceWebSocketClient.class);

        Set<BinanceWebSocketClient> activeConnections = getActiveConnectionsSet();
        activeConnections.add(mockClient1);
        activeConnections.add(mockClient2);

        binanceService.cleanup();

        verify(mockClient1).close();
        verify(mockClient2).close();
        assertTrue(activeConnections.isEmpty());
        assertTrue(getStreamToClientMap().isEmpty());
    }

    @Test
    void shouldHandleNullConfigPairs() {
        when(gatewayConfig.getPairs()).thenReturn(null);

        binanceService.refreshPairs();

        Set<String> currentPairs = getCurrentPairsSet();
        assertTrue(currentPairs.isEmpty());
    }

    @Test
    void shouldPartitionLargeListOfPairs() {
        List<String> largePairsList = new ArrayList<>();
        for (int i = 0; i < 250; i++) {
            largePairsList.add("PAIR" + i + "USDT");
        }

        when(gatewayConfig.getPairs()).thenReturn(largePairsList);

        List<List<String>> result = ReflectionTestUtils.invokeMethod(
                binanceService,
                "partitionList",
                largePairsList
        );

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(200, result.get(0).size());
        assertEquals(50, result.get(1).size());
    }

    @Test
    void shouldHandleEmptyPairsListOnRefresh() {
        when(gatewayConfig.getPairs()).thenReturn(Collections.emptyList());

        binanceService.refreshPairs();

        Set<String> currentPairs = getCurrentPairsSet();
        assertTrue(currentPairs.isEmpty());
        assertTrue(getStreamToClientMap().isEmpty());
    }

    @Test
    void shouldNotProcessMessageWhenKafkaFails() throws Exception {
        String validMessage = createValidMessage();

        doThrow(new RuntimeException("Kafka unavailable"))
                .when(kafkaProducerService).sendCryptoPriceEvent(anyString(), anyString());

        binanceService.processMessage(validMessage);

        verify(kafkaProducerService).sendCryptoPriceEvent(anyString(), anyString());
    }

    @Test
    void shouldHandleConcurrentRefreshAndProcessMessage() throws InterruptedException {
        when(gatewayConfig.getPairs()).thenReturn(Arrays.asList("BTCUSDT", "ETHUSDT"));

        Thread refreshThread = new Thread(() -> {
            for (int i = 0; i < 100; i++) {
                binanceService.refreshPairs();
            }
        });

        Thread processThread = new Thread(() -> {
            try {
                String message = createValidMessage();
                for (int i = 0; i < 100; i++) {
                    binanceService.processMessage(message);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });

        refreshThread.start();
        processThread.start();
        refreshThread.join();
        processThread.join();

        verify(kafkaProducerService, atLeastOnce()).sendCryptoPriceEvent(anyString(), anyString());
    }

    @Test
    void shouldHandleNullValuesInMessageProcessing() {
        binanceService.processMessage(null);

        String messageWithNulls = "{\"data\":{\"s\":null,\"c\":null,\"v\":null,\"E\":null}}";
        binanceService.processMessage(messageWithNulls);

        verify(kafkaProducerService, never()).sendCryptoPriceEvent(anyString(), anyString());
    }

    private String createValidMessage() throws Exception {
        ObjectNode dataNode = objectMapper.createObjectNode()
                .put("s", "BTCUSDT")
                .put("c", "50000.00")
                .put("v", "1.5")
                .put("E", 1621234567890L);

        ObjectNode rootNode = objectMapper.createObjectNode();
        rootNode.set("data", dataNode);

        return objectMapper.writeValueAsString(rootNode);
    }

    @SuppressWarnings("unchecked")
    private Map<String, BinanceWebSocketClient> getStreamToClientMap() {
        return (ConcurrentHashMap<String, BinanceWebSocketClient>) ReflectionTestUtils.getField(binanceService, "streamToClientMap");
    }

    @SuppressWarnings("unchecked")
    private Set<BinanceWebSocketClient> getActiveConnectionsSet() {
        return (Set<BinanceWebSocketClient>) ReflectionTestUtils.getField(binanceService, "activeConnections");
    }

    @SuppressWarnings("unchecked")
    private Set<String> getCurrentPairsSet() {
        return (Set<String>) ReflectionTestUtils.getField(binanceService, "currentPairs");
    }
}