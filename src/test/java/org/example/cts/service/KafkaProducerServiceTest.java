package org.example.cts.service;

import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.retry.annotation.Retryable;
import java.lang.reflect.Method;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class KafkaProducerServiceTest {

    @Mock
    private KafkaTemplate<String, String> kafkaTemplate;

    @InjectMocks
    private KafkaProducerService kafkaProducerService;

    @Captor
    private ArgumentCaptor<ProducerRecord<String, String>> recordCaptor;

    @Test
    void shouldHaveRetryableAnnotation() throws NoSuchMethodException {
        Method method = KafkaProducerService.class.getMethod("sendCryptoPriceEvent", String.class, String.class);
        Retryable retryable = method.getAnnotation(Retryable.class);

        assertNotNull(retryable);
        assertEquals(3, retryable.maxAttempts());
        assertEquals(1000, retryable.backoff().delay());
        assertEquals(2, retryable.backoff().multiplier());
    }

    @Test
    void shouldSendMessageToKafka() {
        String topic = "crypto.prices";
        String message = "test message";

        CompletableFuture<SendResult<String, String>> future = new CompletableFuture<>();
        when(kafkaTemplate.send(any(ProducerRecord.class))).thenReturn(future);

        kafkaProducerService.sendCryptoPriceEvent(topic, message);

        verify(kafkaTemplate).send(recordCaptor.capture());
        ProducerRecord<String, String> capturedRecord = recordCaptor.getValue();

        assertEquals(topic, capturedRecord.topic());
        assertEquals(message, capturedRecord.value());
        assertNull(capturedRecord.key());
    }

    @Test
    void shouldThrowIllegalArgumentExceptionForNullTopic() {
        assertThrows(IllegalArgumentException.class,
                () -> kafkaProducerService.sendCryptoPriceEvent(null, "message"));
    }

    @Test
    void shouldThrowNullPointerExceptionForEmptyTopic() {
        assertThrows(NullPointerException.class,
                () -> kafkaProducerService.sendCryptoPriceEvent("", "message"));
    }

    @Test
    void shouldThrowNullPointerExceptionForNullMessage() {
        assertThrows(NullPointerException.class,
                () -> kafkaProducerService.sendCryptoPriceEvent("topic", null));
    }

    @Test
    void shouldCompleteExceptionallyWhenKafkaFails() {
        String topic = "crypto.prices";
        String message = "test message";
        when(kafkaTemplate.send(any(ProducerRecord.class)))
                .thenThrow(new RuntimeException("Kafka unavailable"));

        assertThrows(RuntimeException.class,
                () -> kafkaProducerService.sendCryptoPriceEvent(topic, message));
    }

    @Test
    void shouldLogSuccessWhenKafkaSendsSuccessfully() {
        String topic = "crypto.prices";
        String message = "test message";

        CompletableFuture<SendResult<String, String>> future = new CompletableFuture<>();
        when(kafkaTemplate.send(any(ProducerRecord.class))).thenReturn(future);

        kafkaProducerService.sendCryptoPriceEvent(topic, message);

        future.complete(mock(SendResult.class));

        verify(kafkaTemplate).send(recordCaptor.capture());
        assertEquals(topic, recordCaptor.getValue().topic());
    }

    @Test
    void shouldLogErrorWhenKafkaSendFailsAsynchronously() {
        String topic = "crypto.prices";
        String message = "test message";

        CompletableFuture<SendResult<String, String>> future = new CompletableFuture<>();
        when(kafkaTemplate.send(any(ProducerRecord.class))).thenReturn(future);

        kafkaProducerService.sendCryptoPriceEvent(topic, message);

        future.completeExceptionally(new RuntimeException("Kafka async failure"));

        verify(kafkaTemplate).send(recordCaptor.capture());
        assertEquals(topic, recordCaptor.getValue().topic());
    }
}