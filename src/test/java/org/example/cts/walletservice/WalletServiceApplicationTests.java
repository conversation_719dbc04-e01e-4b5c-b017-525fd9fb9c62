package org.example.cts.walletservice;

import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest
@ActiveProfiles("test")
class WalletServiceApplicationTests {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    void contextLoads() {
        assertNotNull(applicationContext, "Application context should not be null");
    }

    @Test
    void applicationNameShouldBeSet() {
        String applicationName = applicationContext.getEnvironment().getProperty("spring.application.name");
        assertNotNull(applicationName, "Application name should be set");
    }

    @Test
    void mainMethodShouldStartApplication() {
        try (MockedStatic<SpringApplication> mockedSpringApplication = Mockito.mockStatic(SpringApplication.class)) {
            String[] args = new String[0];

            assertDoesNotThrow(() -> WalletServiceApplication.main(args), "Main method should not throw exceptions");

            mockedSpringApplication.verify(() ->
                            SpringApplication.run(WalletServiceApplication.class, args),
                    Mockito.times(1));
        }
    }

}
