package org.example.cts.orderservice.model.enumType;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class OrderTypeTest {

    @Test
    void testOrderTypeValues() {
        // Then
        assertEquals(2, OrderType.values().length);
        assertEquals(OrderType.LIMIT, OrderType.valueOf("LIMIT"));
        assertEquals(OrderType.MARKET, OrderType.valueOf("MARKET"));
    }

    @Test
    void testOrderTypeEnumeration() {
        // Then
        assertArrayEquals(
                new OrderType[]{OrderType.LIMIT, OrderType.MARKET},
                OrderType.values()
        );
    }
}
