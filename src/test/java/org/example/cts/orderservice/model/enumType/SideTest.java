package org.example.cts.orderservice.model.enumType;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class SideTest {

    @Test
    void testSideValues() {
        // Then
        assertEquals(2, Side.values().length);
        assertEquals(Side.BUY, Side.valueOf("BUY"));
        assertEquals(Side.SELL, Side.valueOf("SELL"));
    }

    @Test
    void testSideEnumeration() {
        // Then
        assertArrayEquals(
                new Side[]{Side.BUY, Side.SELL},
                Side.values()
        );
    }
}
