package org.example.cts.orderservice.model.enumType;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class OrderStatusTest {

    @Test
    void testOrderStatusValues() {
        // Then
        assertEquals(5, OrderStatus.values().length);
        assertEquals(OrderStatus.OPEN, OrderStatus.valueOf("OPEN"));
        assertEquals(OrderStatus.PARTIALLY_FILLED, OrderStatus.valueOf("PARTIALLY_FILLED"));
        assertEquals(OrderStatus.FILLED, OrderStatus.valueOf("FILLED"));
        assertEquals(OrderStatus.CANCELLED, OrderStatus.valueOf("CANCELLED"));
        assertEquals(OrderStatus.REJECTED, OrderStatus.valueOf("REJECTED"));
    }

    @Test
    void testOrderStatusEnumeration() {
        // Then
        assertArrayEquals(
                new OrderStatus[]{
                        OrderStatus.OPEN,
                        OrderStatus.PARTIALLY_FILLED,
                        OrderStatus.FILLED,
                        OrderStatus.CANCELLED,
                        OrderStatus.REJECTED
                },
                OrderStatus.values()
        );
    }
}
