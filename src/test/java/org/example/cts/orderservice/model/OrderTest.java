package org.example.cts.orderservice.model;

import org.example.cts.orderservice.model.enumType.OrderStatus;
import org.example.cts.orderservice.model.enumType.OrderType;
import org.example.cts.orderservice.model.enumType.Side;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class OrderTest {

    @Test
    void testOrderBuilder() {
        // Given
        String id = "order123";
        UUID userId = UUID.randomUUID();
        String tradingPair = "BTC/USD";
        OrderType orderType = OrderType.LIMIT;
        Side side = Side.BUY;
        BigDecimal amount = new BigDecimal("1.0");
        BigDecimal filledAmount = new BigDecimal("0.5");
        BigDecimal price = new BigDecimal("50000.0");
        OrderStatus orderStatus = OrderStatus.PARTIALLY_FILLED;
        LocalDateTime createdAt = LocalDateTime.now();
        LocalDateTime updatedAt = LocalDateTime.now();

        // When
        Order order = Order.builder()
                .id(id)
                .userId(userId)
                .tradingPair(tradingPair)
                .orderType(orderType)
                .side(side)
                .amount(amount)
                .filledAmount(filledAmount)
                .price(price)
                .orderStatus(orderStatus)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();

        // Then
        assertEquals(id, order.getId());
        assertEquals(userId, order.getUserId());
        assertEquals(tradingPair, order.getTradingPair());
        assertEquals(orderType, order.getOrderType());
        assertEquals(side, order.getSide());
        assertEquals(amount, order.getAmount());
        assertEquals(filledAmount, order.getFilledAmount());
        assertEquals(price, order.getPrice());
        assertEquals(orderStatus, order.getOrderStatus());
        assertEquals(createdAt, order.getCreatedAt());
        assertEquals(updatedAt, order.getUpdatedAt());
    }

    @Test
    void testOrderGettersAndSetters() {
        // Given
        Order order = new Order();
        String id = "order456";
        UUID userId = UUID.randomUUID();
        String tradingPair = "ETH/USD";
        OrderType orderType = OrderType.MARKET;
        Side side = Side.SELL;
        BigDecimal amount = new BigDecimal("2.0");
        BigDecimal filledAmount = new BigDecimal("2.0");
        BigDecimal price = new BigDecimal("3000.0");
        OrderStatus orderStatus = OrderStatus.FILLED;
        LocalDateTime createdAt = LocalDateTime.now();
        LocalDateTime updatedAt = LocalDateTime.now();

        // When
        order.setId(id);
        order.setUserId(userId);
        order.setTradingPair(tradingPair);
        order.setOrderType(orderType);
        order.setSide(side);
        order.setAmount(amount);
        order.setFilledAmount(filledAmount);
        order.setPrice(price);
        order.setOrderStatus(orderStatus);
        order.setCreatedAt(createdAt);
        order.setUpdatedAt(updatedAt);

        // Then
        assertEquals(id, order.getId());
        assertEquals(userId, order.getUserId());
        assertEquals(tradingPair, order.getTradingPair());
        assertEquals(orderType, order.getOrderType());
        assertEquals(side, order.getSide());
        assertEquals(amount, order.getAmount());
        assertEquals(filledAmount, order.getFilledAmount());
        assertEquals(price, order.getPrice());
        assertEquals(orderStatus, order.getOrderStatus());
        assertEquals(createdAt, order.getCreatedAt());
        assertEquals(updatedAt, order.getUpdatedAt());
    }

    @Test
    void testNoArgsConstructor() {
        // When
        Order order = new Order();

        // Then
        assertNull(order.getId());
        assertNull(order.getUserId());
        assertNull(order.getTradingPair());
        assertNull(order.getOrderType());
        assertNull(order.getSide());
        assertNull(order.getAmount());
        assertNull(order.getFilledAmount());
        assertNull(order.getPrice());
        assertNull(order.getOrderStatus());
        assertNull(order.getCreatedAt());
        assertNull(order.getUpdatedAt());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        String id = "order789";
        UUID userId = UUID.randomUUID();
        String tradingPair = "XRP/USD";
        OrderType orderType = OrderType.LIMIT;
        Side side = Side.BUY;
        BigDecimal amount = new BigDecimal("100.0");
        BigDecimal filledAmount = BigDecimal.ZERO;
        BigDecimal price = new BigDecimal("1.2");
        OrderStatus orderStatus = OrderStatus.OPEN;
        LocalDateTime createdAt = LocalDateTime.now();
        LocalDateTime updatedAt = LocalDateTime.now();

        // When
        Order order = new Order(
                id, userId, tradingPair, orderType, side, amount, 
                filledAmount, price, orderStatus, createdAt, updatedAt
        );

        // Then
        assertEquals(id, order.getId());
        assertEquals(userId, order.getUserId());
        assertEquals(tradingPair, order.getTradingPair());
        assertEquals(orderType, order.getOrderType());
        assertEquals(side, order.getSide());
        assertEquals(amount, order.getAmount());
        assertEquals(filledAmount, order.getFilledAmount());
        assertEquals(price, order.getPrice());
        assertEquals(orderStatus, order.getOrderStatus());
        assertEquals(createdAt, order.getCreatedAt());
        assertEquals(updatedAt, order.getUpdatedAt());
    }
}
