package org.example.cts.kafka;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.example.cts.dto.CryptoPriceEvent;
import org.example.cts.utils.TestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.kafka.core.KafkaTemplate;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class KafkaForwarderServiceTest {

    private KafkaTemplate<String, String> kafkaTemplate;
    private ObjectMapper objectMapper;
    private KafkaForwarderService kafkaForwarderService;

    @BeforeEach
    void setUp() throws Exception {
        kafkaTemplate = mock(KafkaTemplate.class);

        objectMapper = mock(ObjectMapper.class);
        when(objectMapper.readValue(anyString(), eq(CryptoPriceEvent.class)))
                .thenReturn(new CryptoPriceEvent(
                        "BTCUSDC",
                        new java.math.BigDecimal("68923.15"),
                        new java.math.BigDecimal("112.5"),
                        java.time.Instant.parse("2025-05-09T08:15:30Z")
                ));
        kafkaForwarderService = new KafkaForwarderService(kafkaTemplate, objectMapper);
        TestUtils.setField(kafkaForwarderService, "outputTopic", "test.output.topic");
    }


    @Test
    void shouldParseAndForwardMessage() throws Exception {
        String json = "{\"symbol\":\"BTCUSDC\",\"price\":68923.15,\"volume\":112.5,\"timestamp\":\"2025-05-09T08:15:30Z\"}";

        kafkaForwarderService.listenAndForward(json);

        ArgumentCaptor<String> topicCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> messageCaptor = ArgumentCaptor.forClass(String.class);

        verify(kafkaTemplate, times(1)).send(topicCaptor.capture(), messageCaptor.capture());

        assertEquals("test.output.topic", topicCaptor.getValue());
        assertEquals(json, messageCaptor.getValue());

        verify(objectMapper).readValue(json, CryptoPriceEvent.class);
    }

    @Test
    void shouldHandleInvalidJsonGracefully() throws Exception {
        String invalidJson = "{invalid";

        when(objectMapper.readValue(eq(invalidJson), eq(CryptoPriceEvent.class)))
                .thenThrow(new RuntimeException("Invalid JSON"));

        kafkaForwarderService.listenAndForward(invalidJson);

        verify(kafkaTemplate, never()).send(anyString(), anyString());
    }
}
